@mixin themeify($property, $variable) {
  #{$property}: var(#{$variable});
}

@mixin disabledProperty($opacity, $pointer, $cursor) {
  opacity: $opacity !important;
  pointer-events: $pointer !important;
  cursor: $cursor !important;
}

@mixin prompt-bar-style($bg, $animation-name, $hover-border-color) {
  background: $bg;
  border: 2px solid transparent;
  transition:
    all 0.3s ease-in-out,
    border-color 0.3s ease-out !important;
  animation: #{$animation-name} 3s ease-in-out infinite;
  &:hover {
    border-color: $hover-border-color !important;
    transition: border-color 0.3s ease-in !important;
  }
  &:focus-within {
    animation: #{$animation-name} 3s ease-in-out infinite;
  }

  &.processing {
    animation: #{if(
        $animation-name == 'promptBarBreatheDark',
        'promptBarProcessingDark',
        'promptBarProcessingLight'
      )}
      2s ease-in-out infinite;
  }
}

@mixin transition($properties...) {
  transition: $properties;
}

@mixin card-styles($bg, $border-color, $shadow) {
  background: $bg;
  border: 2px solid $border-color;
  box-shadow: $shadow;
  backdrop-filter: blur(7.5px);
  @include transition(all 0.3s ease);
}

@mixin button-hover {
  &:hover {
    opacity: 0.8;
  }
}
