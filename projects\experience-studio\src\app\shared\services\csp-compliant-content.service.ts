import { Injectable, inject } from '@angular/core';
import { environment } from '../../../environments/environment';

/**
 * Service to handle CSP-compliant HTML content processing for wireframe generation
 * Replaces external resources with local alternatives or inline content
 */
@Injectable({
  providedIn: 'root'
})
export class CSPCompliantContentService {

  /**
   * Process HTML content to make it CSP-compliant for production
   * @param htmlContent Original HTML content with external resources
   * @returns CSP-compliant HTML content
   */
  processHTMLContent(htmlContent: string): string {
    if (!htmlContent) return '';

    let processedContent = htmlContent;

    // In development mode, be less aggressive with replacements
    if (!environment.production) {
      // Only add CSP meta tags and basic error handling in development
      processedContent = this.addCSPCompliantMetaTags(processedContent);
      processedContent = this.addDevelopmentErrorHandling(processedContent);
      return processedContent;
    }

    // Production mode: full processing
    // Replace external CDN resources with local alternatives
    processedContent = this.replaceExternalResources(processedContent);

    // Inline critical CSS frameworks
    processedContent = this.inlineCriticalCSS(processedContent);

    // Replace external scripts with local alternatives
    processedContent = this.replaceExternalScripts(processedContent);

    // Fix font references
    processedContent = this.fixFontReferences(processedContent);

    // Add CSP-compliant meta tags
    processedContent = this.addCSPCompliantMetaTags(processedContent);

    return processedContent;
  }

  /**
   * Replace external CDN resources with local alternatives
   */
  private replaceExternalResources(content: string): string {
    let processed = content;

    // Replace Tailwind CSS CDN with local version or inline styles
    processed = processed.replace(
      /https:\/\/cdn\.tailwindcss\.com[^"']*/g,
      ''
    );

    processed = processed.replace(
      /https:\/\/unpkg\.com\/tailwindcss[^"']*/g,
      ''
    );

    // Replace Lucide icons CDN with local alternative
    processed = processed.replace(
      /https:\/\/unpkg\.com\/lucide@latest[^"']*/g,
      ''
    );

    // Replace custom font URLs with safe alternatives
    processed = processed.replace(
      /https:\/\/www\.shiftkey\.com\/fonts\/[^"']*/g,
      ''
    );

    // Handle external images - add error handling for failed loads
    processed = this.addImageErrorHandling(processed);

    return processed;
  }

  /**
   * Inline critical CSS frameworks to avoid external dependencies
   */
  private inlineCriticalCSS(content: string): string {
    const criticalTailwindCSS = this.getCriticalTailwindCSS();
    const lucideIconsCSS = this.getLucideIconsCSS();
    
    // Find head tag or create one
    if (content.includes('<head>')) {
      return content.replace(
        '<head>',
        `<head>
        <style>
          ${criticalTailwindCSS}
          ${lucideIconsCSS}
        </style>`
      );
    } else if (content.includes('<body>')) {
      return content.replace(
        '<body>',
        `<body>
        <style>
          ${criticalTailwindCSS}
          ${lucideIconsCSS}
        </style>`
      );
    }
    
    return `<style>${criticalTailwindCSS}${lucideIconsCSS}</style>${content}`;
  }

  /**
   * Replace external scripts with CSP-compliant alternatives
   */
  private replaceExternalScripts(content: string): string {
    let processed = content;

    // Replace Lucide icons script with inline SVG definitions
    processed = processed.replace(
      /<script[^>]*src=["']https:\/\/unpkg\.com\/lucide@latest[^"']*["'][^>]*><\/script>/g,
      this.getLucideIconsScript()
    );

    // Replace Chart.js CDN with local alternative or keep it for development
    processed = processed.replace(
      /<script[^>]*src=["']https:\/\/cdn\.jsdelivr\.net\/npm\/chart\.js[^"']*["'][^>]*><\/script>/g,
      this.getChartJsScript()
    );

    // Replace any other external scripts
    processed = processed.replace(
      /<script[^>]*src=["']https:\/\/cdn\.tailwindcss\.com[^"']*["'][^>]*><\/script>/g,
      ''
    );

    return processed;
  }

  /**
   * Fix font references to use safe alternatives
   */
  private fixFontReferences(content: string): string {
    let processed = content;

    // Replace custom font references with system fonts
    processed = processed.replace(
      /font-family:\s*["']?MintGrotesk[^"';]*/g,
      'font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
    );

    processed = processed.replace(
      /font-family:\s*["']?MintGroteskDisplay[^"';]*/g,
      'font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
    );

    return processed;
  }

  /**
   * Add CSP-compliant meta tags
   */
  private addCSPCompliantMetaTags(content: string): string {
    // Use more permissive CSP for development, stricter for production
    const cspPolicy = environment.production
      ? "default-src 'self' https: data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https: data: blob:; style-src 'self' 'unsafe-inline' https: data:; img-src 'self' https: data: blob:; font-src 'self' https: data: blob:; connect-src 'self' https:; frame-src 'self' https: data: blob:; worker-src 'self' blob:;"
      : "default-src 'self' https: http: data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https: http: data: blob:; style-src 'self' 'unsafe-inline' https: http: data:; img-src 'self' https: http: data: blob:; font-src 'self' https: http: data: blob:; connect-src 'self' https: http:; frame-src 'self' https: http: data: blob:; worker-src 'self' blob:;";

    const metaTags = `
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta http-equiv="Content-Security-Policy" content="${cspPolicy}">
    `;

    if (content.includes('<head>')) {
      return content.replace('<head>', `<head>${metaTags}`);
    } else {
      return `<head>${metaTags}</head>${content}`;
    }
  }

  /**
   * Get critical Tailwind CSS classes commonly used in wireframes
   */
  private getCriticalTailwindCSS(): string {
    return `
      /* Critical Tailwind CSS Reset and Base */
      *, ::before, ::after { box-sizing: border-box; border-width: 0; border-style: solid; border-color: #e5e7eb; }
      ::before, ::after { --tw-content: ''; }
      html { line-height: 1.5; -webkit-text-size-adjust: 100%; -moz-tab-size: 4; tab-size: 4; font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif; }
      body { margin: 0; line-height: inherit; }
      
      /* Common Tailwind Utilities */
      .flex { display: flex; }
      .block { display: block; }
      .inline-block { display: inline-block; }
      .hidden { display: none; }
      .w-full { width: 100%; }
      .h-full { height: 100%; }
      .min-h-screen { min-height: 100vh; }
      .p-4 { padding: 1rem; }
      .p-6 { padding: 1.5rem; }
      .p-8 { padding: 2rem; }
      .px-4 { padding-left: 1rem; padding-right: 1rem; }
      .py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
      .py-4 { padding-top: 1rem; padding-bottom: 1rem; }
      .m-4 { margin: 1rem; }
      .mx-auto { margin-left: auto; margin-right: auto; }
      .mb-4 { margin-bottom: 1rem; }
      .mb-6 { margin-bottom: 1.5rem; }
      .text-center { text-align: center; }
      .text-left { text-align: left; }
      .text-right { text-align: right; }
      .font-bold { font-weight: 700; }
      .font-semibold { font-weight: 600; }
      .text-lg { font-size: 1.125rem; line-height: 1.75rem; }
      .text-xl { font-size: 1.25rem; line-height: 1.75rem; }
      .text-2xl { font-size: 1.5rem; line-height: 2rem; }
      .text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
      .bg-white { background-color: rgb(255 255 255); }
      .bg-gray-100 { background-color: rgb(243 244 246); }
      .bg-gray-200 { background-color: rgb(229 231 235); }
      .bg-blue-500 { background-color: rgb(59 130 246); }
      .bg-blue-600 { background-color: rgb(37 99 235); }
      .text-white { color: rgb(255 255 255); }
      .text-gray-600 { color: rgb(75 85 99); }
      .text-gray-800 { color: rgb(31 41 55); }
      .text-blue-600 { color: rgb(37 99 235); }
      .border { border-width: 1px; }
      .border-gray-300 { border-color: rgb(209 213 219); }
      .rounded { border-radius: 0.25rem; }
      .rounded-lg { border-radius: 0.5rem; }
      .shadow { box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1); }
      .shadow-lg { box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1); }
      .hover\\:bg-blue-700:hover { background-color: rgb(29 78 216); }
      .focus\\:outline-none:focus { outline: 2px solid transparent; outline-offset: 2px; }
      .transition { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
      .cursor-pointer { cursor: pointer; }
      .select-none { user-select: none; }
      .items-center { align-items: center; }
      .justify-center { justify-content: center; }
      .justify-between { justify-content: space-between; }
      .space-y-4 > :not([hidden]) ~ :not([hidden]) { margin-top: 1rem; }
      .space-x-4 > :not([hidden]) ~ :not([hidden]) { margin-left: 1rem; }
      .grid { display: grid; }
      .grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
      .grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
      .grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
      .gap-4 { gap: 1rem; }
      .gap-6 { gap: 1.5rem; }
      .max-w-md { max-width: 28rem; }
      .max-w-lg { max-width: 32rem; }
      .max-w-xl { max-width: 36rem; }
      .max-w-2xl { max-width: 42rem; }
      .max-w-4xl { max-width: 56rem; }
      .container { width: 100%; }
      @media (min-width: 640px) { .container { max-width: 640px; } }
      @media (min-width: 768px) { .container { max-width: 768px; } }
      @media (min-width: 1024px) { .container { max-width: 1024px; } }
      @media (min-width: 1280px) { .container { max-width: 1280px; } }
    `;
  }

  /**
   * Get Lucide icons CSS for common icons
   */
  private getLucideIconsCSS(): string {
    return `
      /* Lucide Icons Base Styles */
      .lucide { 
        display: inline-block; 
        width: 24px; 
        height: 24px; 
        stroke: currentColor; 
        stroke-width: 2; 
        stroke-linecap: round; 
        stroke-linejoin: round; 
        fill: none; 
      }
      .lucide-sm { width: 16px; height: 16px; }
      .lucide-lg { width: 32px; height: 32px; }
    `;
  }

  /**
   * Get Lucide icons script replacement with inline SVG definitions
   */
  private getLucideIconsScript(): string {
    return `
      <script>
        // CSP-compliant Lucide icons replacement
        window.lucide = {
          createIcons: function() {
            // Replace lucide-* classes with inline SVGs
            document.querySelectorAll('[class*="lucide-"]').forEach(function(el) {
              const iconName = Array.from(el.classList).find(cls => cls.startsWith('lucide-'));
              if (iconName) {
                const svgContent = window.lucide.getIconSVG(iconName.replace('lucide-', ''));
                if (svgContent) {
                  el.innerHTML = svgContent;
                }
              }
            });
          },
          getIconSVG: function(name) {
            const icons = {
              'menu': '<svg class="lucide" viewBox="0 0 24 24"><path d="M3 12h18M3 6h18M3 18h18"/></svg>',
              'x': '<svg class="lucide" viewBox="0 0 24 24"><path d="M18 6L6 18M6 6l12 12"/></svg>',
              'home': '<svg class="lucide" viewBox="0 0 24 24"><path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/><polyline points="9,22 9,12 15,12 15,22"/></svg>',
              'user': '<svg class="lucide" viewBox="0 0 24 24"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/><circle cx="12" cy="7" r="4"/></svg>',
              'search': '<svg class="lucide" viewBox="0 0 24 24"><circle cx="11" cy="11" r="8"/><path d="m21 21-4.35-4.35"/></svg>',
              'heart': '<svg class="lucide" viewBox="0 0 24 24"><path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/></svg>',
              'star': '<svg class="lucide" viewBox="0 0 24 24"><polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/></svg>',
              'shopping-cart': '<svg class="lucide" viewBox="0 0 24 24"><circle cx="9" cy="21" r="1"/><circle cx="20" cy="21" r="1"/><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"/></svg>',
              'mail': '<svg class="lucide" viewBox="0 0 24 24"><path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/><polyline points="22,6 12,13 2,6"/></svg>',
              'phone': '<svg class="lucide" viewBox="0 0 24 24"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/></svg>',
              'arrow-right': '<svg class="lucide" viewBox="0 0 24 24"><path d="M5 12h14m-7-7 7 7-7 7"/></svg>',
              'arrow-left': '<svg class="lucide" viewBox="0 0 24 24"><path d="M19 12H5m7 7-7-7 7-7"/></svg>',
              'check': '<svg class="lucide" viewBox="0 0 24 24"><path d="M20 6L9 17l-5-5"/></svg>',
              'plus': '<svg class="lucide" viewBox="0 0 24 24"><path d="M12 5v14m-7-7h14"/></svg>',
              'minus': '<svg class="lucide" viewBox="0 0 24 24"><path d="M5 12h14"/></svg>',
              'edit': '<svg class="lucide" viewBox="0 0 24 24"><path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/><path d="m18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z"/></svg>',
              'trash': '<svg class="lucide" viewBox="0 0 24 24"><path d="M3 6h18m-2 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"/></svg>',
              'settings': '<svg class="lucide" viewBox="0 0 24 24"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/><circle cx="12" cy="12" r="3"/></svg>'
            };
            return icons[name] || '<svg class="lucide" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10"/></svg>';
          }
        };

        // Auto-initialize icons when DOM is ready
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', window.lucide.createIcons);
        } else {
          window.lucide.createIcons();
        }
      </script>
    `;
  }

  /**
   * Process wireframe-specific content issues
   * @param content HTML content from wireframe generation
   * @returns Processed content with wireframe-specific fixes
   */
  processWireframeContent(content: string): string {
    if (!content) return '';

    let processed = this.processHTMLContent(content);

    // Additional wireframe-specific processing
    processed = this.fixWireframeSpecificIssues(processed);

    return processed;
  }

  /**
   * Fix wireframe-specific issues like undefined variables and missing dependencies
   */
  private fixWireframeSpecificIssues(content: string): string {
    let processed = content;

    // Fix undefined lucide variable by ensuring the script is loaded first
    processed = processed.replace(
      /<script[^>]*>[\s\S]*?lucide\.createIcons\(\)[\s\S]*?<\/script>/g,
      (match) => {
        // Ensure lucide is defined before calling createIcons
        return match.replace(
          'lucide.createIcons()',
          'if (typeof lucide !== "undefined") { lucide.createIcons(); }'
        );
      }
    );

    // Add error handling for missing dependencies
    const errorHandlingScript = `
      <script>
        // Global error handler for missing dependencies
        window.addEventListener('error', function(e) {
          if (e.message && e.message.includes('lucide is not defined')) {
            console.warn('Lucide icons not loaded, using fallback');
            // Create minimal fallback
            window.lucide = { createIcons: function() {} };
          }
        });
      </script>
    `;

    // Add error handling script at the beginning
    if (processed.includes('<head>')) {
      processed = processed.replace('<head>', `<head>${errorHandlingScript}`);
    } else {
      processed = `${errorHandlingScript}${processed}`;
    }

    return processed;
  }

  /**
   * Get Chart.js script replacement with inline Chart.js library
   */
  private getChartJsScript(): string {
    return `
      <script>
        // Minimal Chart.js implementation for CSP compliance
        // This is a simplified version that provides basic chart functionality
        window.Chart = function(ctx, config) {
          this.ctx = ctx;
          this.config = config;
          this.data = config.data || {};
          this.options = config.options || {};

          // Simple canvas-based chart rendering
          this.render = function() {
            if (!ctx || !ctx.getContext) return;

            const canvas = ctx.canvas || ctx;
            const context = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;

            // Clear canvas
            context.clearRect(0, 0, width, height);

            // Basic chart rendering based on type
            if (config.type === 'bar') {
              this.renderBarChart(context, width, height);
            } else if (config.type === 'line') {
              this.renderLineChart(context, width, height);
            } else if (config.type === 'pie' || config.type === 'doughnut') {
              this.renderPieChart(context, width, height);
            } else {
              // Fallback: render a simple placeholder
              this.renderPlaceholder(context, width, height);
            }
          };

          this.renderBarChart = function(ctx, width, height) {
            const data = this.data.datasets?.[0]?.data || [];
            const labels = this.data.labels || [];
            const barWidth = width / (data.length * 1.5);
            const maxValue = Math.max(...data);

            ctx.fillStyle = '#3B82F6';
            data.forEach((value, index) => {
              const barHeight = (value / maxValue) * (height * 0.7);
              const x = (index * barWidth * 1.5) + (width * 0.1);
              const y = height - barHeight - (height * 0.1);
              ctx.fillRect(x, y, barWidth, barHeight);
            });
          };

          this.renderLineChart = function(ctx, width, height) {
            const data = this.data.datasets?.[0]?.data || [];
            const maxValue = Math.max(...data);
            const minValue = Math.min(...data);
            const range = maxValue - minValue || 1;

            ctx.strokeStyle = '#3B82F6';
            ctx.lineWidth = 2;
            ctx.beginPath();

            data.forEach((value, index) => {
              const x = (index / (data.length - 1)) * (width * 0.8) + (width * 0.1);
              const y = height - (((value - minValue) / range) * (height * 0.7)) - (height * 0.1);

              if (index === 0) {
                ctx.moveTo(x, y);
              } else {
                ctx.lineTo(x, y);
              }
            });
            ctx.stroke();
          };

          this.renderPieChart = function(ctx, width, height) {
            const data = this.data.datasets?.[0]?.data || [];
            const total = data.reduce((sum, val) => sum + val, 0);
            const centerX = width / 2;
            const centerY = height / 2;
            const radius = Math.min(width, height) * 0.3;

            let currentAngle = 0;
            const colors = ['#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6'];

            data.forEach((value, index) => {
              const sliceAngle = (value / total) * 2 * Math.PI;
              ctx.fillStyle = colors[index % colors.length];
              ctx.beginPath();
              ctx.moveTo(centerX, centerY);
              ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
              ctx.closePath();
              ctx.fill();
              currentAngle += sliceAngle;
            });
          };

          this.renderPlaceholder = function(ctx, width, height) {
            ctx.fillStyle = '#E5E7EB';
            ctx.fillRect(0, 0, width, height);
            ctx.fillStyle = '#6B7280';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Chart Placeholder', width / 2, height / 2);
          };

          // Auto-render if canvas is ready
          if (ctx && ctx.getContext) {
            setTimeout(() => this.render(), 0);
          }
        };

        // Static methods for Chart.js compatibility
        window.Chart.register = function() {};
        window.Chart.defaults = {};

        console.log('Chart.js CSP-compliant fallback loaded');
      </script>
    `;
  }

  /**
   * Add error handling for external images that might fail to load due to CSP
   */
  private addImageErrorHandling(content: string): string {
    // Add onerror handlers to external images
    let processed = content.replace(
      /<img([^>]*src=["']https:\/\/[^"']*["'][^>]*)>/g,
      '<img$1 onerror="this.style.display=\'none\'; console.warn(\'Image blocked by CSP:\', this.src);">'
    );

    // Add a script to handle image loading errors globally
    const imageErrorScript = `
      <script>
        // Global image error handler for CSP-blocked images
        document.addEventListener('DOMContentLoaded', function() {
          const images = document.querySelectorAll('img[src^="https://"]');
          images.forEach(function(img) {
            if (!img.onerror) {
              img.onerror = function() {
                this.style.display = 'none';
                console.warn('Image blocked by CSP:', this.src);

                // Create a placeholder div
                const placeholder = document.createElement('div');
                placeholder.style.cssText = 'width: ' + (this.width || '200px') + '; height: ' + (this.height || '150px') + '; background: #f3f4f6; border: 2px dashed #d1d5db; display: flex; align-items: center; justify-content: center; color: #6b7280; font-size: 14px;';
                placeholder.textContent = 'Image unavailable';
                this.parentNode.insertBefore(placeholder, this);
              };
            }
          });
        });
      </script>
    `;

    // Add the script to the head or beginning of content
    if (processed.includes('<head>')) {
      processed = processed.replace('<head>', `<head>${imageErrorScript}`);
    } else {
      processed = `${imageErrorScript}${processed}`;
    }

    return processed;
  }

  /**
   * Add basic error handling for development mode
   */
  private addDevelopmentErrorHandling(content: string): string {
    const errorHandlingScript = `
      <script>
        // Development mode error handling for external resources
        window.addEventListener('error', function(e) {
          if (e.message && (e.message.includes('Chart is not defined') || e.message.includes('lucide is not defined'))) {
            console.warn('External library not loaded:', e.message);
            console.warn('This is expected in development mode with CSP restrictions');
          }
        });

        // Handle CSP violations in development
        document.addEventListener('securitypolicyviolation', function(e) {
          console.warn('CSP Violation in development:', {
            blockedURI: e.blockedURI,
            violatedDirective: e.violatedDirective,
            originalPolicy: e.originalPolicy
          });
        });

        // Provide fallbacks for common libraries
        window.addEventListener('DOMContentLoaded', function() {
          // Chart.js fallback
          if (typeof Chart === 'undefined' && document.querySelector('canvas')) {
            console.warn('Chart.js not loaded, providing fallback');
            window.Chart = function() {
              console.warn('Chart.js fallback constructor called');
            };
          }

          // Lucide fallback
          if (typeof lucide === 'undefined' && document.querySelector('[class*="lucide-"]')) {
            console.warn('Lucide not loaded, providing fallback');
            window.lucide = {
              createIcons: function() {
                console.warn('Lucide createIcons fallback called');
              }
            };
          }
        });
      </script>
    `;

    // Add the script to the head or beginning of content
    if (content.includes('<head>')) {
      return content.replace('<head>', `<head>${errorHandlingScript}`);
    } else {
      return `${errorHandlingScript}${content}`;
    }
  }
}
