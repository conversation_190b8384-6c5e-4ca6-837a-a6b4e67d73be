import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LucideAngularModule } from 'lucide-angular';

@Component({
  standalone: true,
  selector: 'main-layout',
  imports: [CommonModule, LucideAngularModule],
  templateUrl: './main-layout.component.html',
  styleUrls: ['./main-layout.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class MainLayoutComponent {
  @Input() showRightPane = true;
  @Input() leftPaneTitle?: string;
  @Input() centerPaneTitle?: string;
  @Input() rightPaneTitle?: string;

  isLeftCollapsed = false;

  toggleLeftPane() {
    this.isLeftCollapsed = !this.isLeftCollapsed;
  }
  
}
