@use '../../../../../../public/assets/styles/mixins' as mixins;

// Theme Toggle Component Styles
.theme-toggle-container {
  position: relative;

  .theme-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    background: transparent;
    border: 1px solid var(--border-light);
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &:hover {
      background: var(--nav-item-hover);
      border-color: var(--primary-purple);
      transform: translateY(-1px);
    }

    &:focus {
      outline: 2px solid var(--primary-purple);
      outline-offset: 1px;
    }

    &:active {
      transform: translateY(0);
    }

    .theme-icon {
      position: relative;
      z-index: 2;
      transition: all 0.3s ease;

      &[data-theme="light"] {
        color: #f59e0b;
        transform: rotate(0deg);
      }

      &[data-theme="dark"] {
        color: #6366f1;
        transform: rotate(180deg);
      }

      &[data-theme="system"] {
        color: var(--text-secondary);
        transform: rotate(360deg);
      }

      i {
        font-size: 1rem;
        transition: all 0.3s ease;
      }
    }

    .theme-indicator {
      position: absolute;
      bottom: 0.25rem;
      right: 0.25rem;
      z-index: 3;

      .indicator-dot {
        width: 0.375rem;
        height: 0.375rem;
        border-radius: 50%;
        transition: all 0.3s ease;

        &[data-theme="light"] {
          background: #f59e0b;
          box-shadow: 0 0 0.5rem rgba(245, 158, 11, 0.5);
        }

        &[data-theme="dark"] {
          background: #6366f1;
          box-shadow: 0 0 0.5rem rgba(99, 102, 241, 0.5);
        }

        &[data-theme="system"] {
          background: var(--text-secondary);
          box-shadow: 0 0 0.5rem rgba(107, 114, 128, 0.3);
        }
      }
    }

    // Animated background gradient
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, 
        rgba(245, 158, 11, 0.1) 0%, 
        rgba(99, 102, 241, 0.1) 50%, 
        rgba(140, 101, 247, 0.1) 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
      z-index: 1;
    }

    &:hover::before {
      opacity: 1;
    }
  }

  .theme-options {
    position: absolute;
    top: calc(100% + 0.5rem);
    right: 0;
    background: var(--card-bg);
    border: 1px solid var(--border-light);
    border-radius: 0.75rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    min-width: 140px;
    z-index: 1000;
    animation: slideDown 0.2s ease-out;
    overflow: hidden;

    .theme-option {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      width: 100%;
      padding: 0.75rem 1rem;
      background: transparent;
      border: none;
      color: var(--text-primary);
      font-size: 0.875rem;
      cursor: pointer;
      transition: all 0.2s ease;
      text-align: left;

      &:hover {
        background: var(--nav-item-hover);
      }

      &.active {
        background: rgba(140, 101, 247, 0.1);
        color: var(--primary-purple);

        i {
          color: var(--primary-purple);
        }
      }

      i {
        width: 1rem;
        text-align: center;
        transition: color 0.2s ease;

        &.fa-sun {
          color: #f59e0b;
        }

        &.fa-moon {
          color: #6366f1;
        }

        &.fa-desktop {
          color: var(--text-secondary);
        }
      }

      span {
        font-weight: 500;
      }

      &:first-child {
        border-top-left-radius: 0.75rem;
        border-top-right-radius: 0.75rem;
      }

      &:last-child {
        border-bottom-left-radius: 0.75rem;
        border-bottom-right-radius: 0.75rem;
      }
    }
  }
}

// Animations
@keyframes slideDown {
  0% {
    opacity: 0;
    transform: translateY(-0.5rem) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

// Theme-specific animations
.theme-toggle {
  .theme-icon {
    &[data-theme="light"] i {
      animation: pulse 2s ease-in-out infinite;
    }

    &[data-theme="dark"] i {
      animation: none;
    }

    &[data-theme="system"] i {
      animation: none;
    }
  }
}

// Dark theme overrides
@media (prefers-color-scheme: dark) {
  .theme-toggle-container {
    .theme-toggle {
      border-color: var(--border-light-dark);

      &:hover {
        background: var(--nav-item-hover-dark);
      }
    }

    .theme-options {
      background: var(--card-bg-dark);
      border-color: var(--border-light-dark);

      .theme-option {
        color: var(--text-primary-dark);

        &:hover {
          background: var(--nav-item-hover-dark);
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .theme-toggle-container {
    .theme-toggle {
      width: 2.25rem;
      height: 2.25rem;

      .theme-icon i {
        font-size: 0.875rem;
      }

      .theme-indicator .indicator-dot {
        width: 0.3125rem;
        height: 0.3125rem;
      }
    }

    .theme-options {
      min-width: 120px;

      .theme-option {
        padding: 0.625rem 0.875rem;
        font-size: 0.8125rem;
      }
    }
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .theme-toggle-container {
    .theme-toggle {
      border-width: 2px;

      &:focus {
        outline-width: 3px;
      }
    }

    .theme-options {
      border-width: 2px;

      .theme-option {
        border: 1px solid transparent;

        &:hover {
          border-color: var(--primary-purple);
        }

        &.active {
          border-color: var(--primary-purple);
          font-weight: 600;
        }
      }
    }
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .theme-toggle-container {
    * {
      transition: none !important;
      animation: none !important;
    }

    .theme-toggle {
      &:hover {
        transform: none;
      }

      &:active {
        transform: none;
      }
    }
  }
}
