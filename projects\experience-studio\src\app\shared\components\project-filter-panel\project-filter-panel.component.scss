.filter-panel {
  background: var(--filter-panel-bg, #ffffff);
  border: 1px solid var(--filter-panel-border, #e1e5e9);
  border-radius: 0.5rem;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
  
  .filter-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: var(--filter-header-bg, #f8f9fa);
    border-bottom: 1px solid var(--filter-header-border, #e1e5e9);
    cursor: pointer;
    
    &:hover {
      background: var(--filter-header-hover-bg, #e9ecef);
    }
    
    .filter-title {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-weight: 600;
      color: var(--filter-title-color, #495057);
      
      i {
        color: var(--filter-icon-color, #6c757d);
      }
      
      .filter-count {
        background: var(--filter-count-bg, #8c65f7);
        color: var(--filter-count-color, #ffffff);
        font-size: 0.75rem;
        font-weight: 600;
        padding: 0.125rem 0.375rem;
        border-radius: 0.75rem;
        min-width: 1.25rem;
        text-align: center;
      }
    }
    
    .filter-actions {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      
      .clear-filters-btn {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        padding: 0.25rem 0.5rem;
        background: transparent;
        border: 1px solid var(--clear-btn-border, #dc3545);
        border-radius: 0.25rem;
        color: var(--clear-btn-color, #dc3545);
        font-size: 0.75rem;
        cursor: pointer;
        transition: all 0.2s ease-in-out;
        
        &:hover {
          background: var(--clear-btn-hover-bg, #dc3545);
          color: var(--clear-btn-hover-color, #ffffff);
        }
        
        &:focus {
          outline: 2px solid var(--clear-btn-focus, #dc3545);
          outline-offset: 1px;
        }
      }
      
      .toggle-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 2rem;
        height: 2rem;
        background: transparent;
        border: 1px solid var(--toggle-btn-border, #dee2e6);
        border-radius: 0.25rem;
        color: var(--toggle-btn-color, #6c757d);
        cursor: pointer;
        transition: all 0.2s ease-in-out;
        
        &:hover {
          background: var(--toggle-btn-hover-bg, #e9ecef);
          border-color: var(--toggle-btn-hover-border, #adb5bd);
        }
        
        &:focus {
          outline: 2px solid var(--toggle-btn-focus, #8c65f7);
          outline-offset: 1px;
        }
      }
    }
  }
  
  .filter-content {
    padding: 1rem;
    
    .filter-section {
      margin-bottom: 1.5rem;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .filter-section-label {
        display: block;
        font-weight: 600;
        color: var(--filter-label-color, #495057);
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
      }
      
      .sort-select {
        width: 100%;
        padding: 0.5rem 0.75rem;
        border: 1px solid var(--select-border, #dee2e6);
        border-radius: 0.25rem;
        background: var(--select-bg, #ffffff);
        color: var(--select-color, #495057);
        font-size: 0.875rem;
        cursor: pointer;
        
        &:focus {
          outline: none;
          border-color: var(--select-focus-border, #8c65f7);
          box-shadow: 0 0 0 2px var(--select-focus-shadow, rgba(140, 101, 247, 0.1));
        }
      }
      
      .checkbox-group {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
        
        .checkbox-item {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          cursor: pointer;
          
          input[type="checkbox"] {
            position: absolute;
            opacity: 0;
            pointer-events: none;
            
            &:checked + .checkbox-custom {
              background: var(--checkbox-checked-bg, #8c65f7);
              border-color: var(--checkbox-checked-border, #8c65f7);
              
              &::after {
                opacity: 1;
                transform: scale(1);
              }
            }
            
            &:focus + .checkbox-custom {
              box-shadow: 0 0 0 2px var(--checkbox-focus-shadow, rgba(140, 101, 247, 0.2));
            }
          }
          
          .checkbox-custom {
            position: relative;
            width: 1rem;
            height: 1rem;
            border: 2px solid var(--checkbox-border, #dee2e6);
            border-radius: 0.125rem;
            background: var(--checkbox-bg, #ffffff);
            transition: all 0.2s ease-in-out;
            
            &::after {
              content: '';
              position: absolute;
              top: 50%;
              left: 50%;
              width: 0.25rem;
              height: 0.5rem;
              border: 2px solid #ffffff;
              border-top: none;
              border-left: none;
              transform: translate(-50%, -60%) rotate(45deg) scale(0);
              opacity: 0;
              transition: all 0.2s ease-in-out;
            }
          }
          
          .checkbox-label {
            display: flex;
            align-items: center;
            gap: 0.375rem;
            font-size: 0.875rem;
            color: var(--checkbox-label-color, #495057);
            
            .project-type-indicator {
              width: 0.75rem;
              height: 0.75rem;
              border-radius: 50%;
              border: 1px solid rgba(255, 255, 255, 0.3);
            }
          }
          
          &:hover .checkbox-custom {
            border-color: var(--checkbox-hover-border, #8c65f7);
          }
        }
      }
    }
    
    .filter-summary {
      margin-top: 1.5rem;
      padding-top: 1rem;
      border-top: 1px solid var(--summary-border, #e1e5e9);
      
      .summary-title {
        font-weight: 600;
        color: var(--summary-title-color, #495057);
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
      }
      
      .summary-items {
        display: flex;
        flex-wrap: wrap;
        gap: 0.375rem;
        
        .summary-item {
          padding: 0.25rem 0.5rem;
          background: var(--summary-item-bg, #e9ecef);
          color: var(--summary-item-color, #495057);
          border-radius: 0.25rem;
          font-size: 0.75rem;
          font-weight: 500;
        }
      }
    }
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .filter-panel {
    background: var(--filter-panel-bg-dark, #2d3748);
    border-color: var(--filter-panel-border-dark, #4a5568);
    
    .filter-header {
      background: var(--filter-header-bg-dark, #1a202c);
      border-color: var(--filter-header-border-dark, #4a5568);
      
      &:hover {
        background: var(--filter-header-hover-bg-dark, #2d3748);
      }
      
      .filter-title {
        color: var(--filter-title-color-dark, #f7fafc);
        
        i {
          color: var(--filter-icon-color-dark, #a0aec0);
        }
      }
    }
    
    .filter-content {
      .filter-section-label {
        color: var(--filter-label-color-dark, #f7fafc);
      }
      
      .sort-select {
        background: var(--select-bg-dark, #2d3748);
        border-color: var(--select-border-dark, #4a5568);
        color: var(--select-color-dark, #f7fafc);
      }
      
      .checkbox-custom {
        background: var(--checkbox-bg-dark, #2d3748);
        border-color: var(--checkbox-border-dark, #4a5568);
      }
      
      .checkbox-label {
        color: var(--checkbox-label-color-dark, #f7fafc);
      }
      
      .summary-item {
        background: var(--summary-item-bg-dark, #4a5568);
        color: var(--summary-item-color-dark, #f7fafc);
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .filter-panel {
    .filter-header {
      padding: 0.75rem;
      
      .filter-actions {
        gap: 0.25rem;
        
        .clear-filters-btn {
          padding: 0.1875rem 0.375rem;
        }
        
        .toggle-btn {
          width: 1.75rem;
          height: 1.75rem;
        }
      }
    }
    
    .filter-content {
      padding: 0.75rem;
      
      .filter-section {
        margin-bottom: 1rem;
      }
    }
  }
}
