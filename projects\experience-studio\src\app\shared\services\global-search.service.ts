import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of, delay, map, catchError } from 'rxjs';
import { SearchSuggestion } from '../components/global-search/global-search.component';
import { CommunityProjectsService } from './community-projects.service';

export interface GlobalSearchResponse {
  suggestions: SearchSuggestion[];
  totalResults: number;
  searchTime: number;
}

/**
 * Global Search Service - Handles search functionality across the workspace
 * Provides intelligent search suggestions and results
 * 
 * Features:
 * - Multi-scope search (projects, community, templates, creators)
 * - Intelligent suggestions with ranking
 * - Search analytics and optimization
 * - Caching for performance
 */
@Injectable({
  providedIn: 'root'
})
export class GlobalSearchService {
  private readonly http = inject(HttpClient);
  private readonly communityService = inject(CommunityProjectsService);
  
  // Mock data for development - replace with real API calls
  private readonly mockProjects = [
    {
      id: '1',
      title: 'Dashboard Design System',
      description: 'Modern admin dashboard with React components',
      type: 'wireframe_generation',
      creator: 'Alex Designer',
      rating: 4.8,
      tags: ['react', 'dashboard', 'admin', 'components']
    },
    {
      id: '2',
      title: 'E-commerce Mobile App',
      description: 'Complete mobile shopping experience',
      type: 'app_generation',
      creator: 'Sarah Developer',
      rating: 4.9,
      tags: ['mobile', 'ecommerce', 'shopping', 'react-native']
    },
    {
      id: '3',
      title: 'Social Media Platform',
      description: 'Full-featured social networking application',
      type: 'app_generation',
      creator: 'Mike Fullstack',
      rating: 4.7,
      tags: ['social', 'networking', 'chat', 'media']
    },
    {
      id: '4',
      title: 'Learning Management System',
      description: 'Educational platform with course management',
      type: 'wireframe_generation',
      creator: 'Lisa Educator',
      rating: 4.6,
      tags: ['education', 'learning', 'courses', 'management']
    },
    {
      id: '5',
      title: 'Fitness Tracking App',
      description: 'Health and fitness monitoring application',
      type: 'app_generation',
      creator: 'Tom Fitness',
      rating: 4.5,
      tags: ['fitness', 'health', 'tracking', 'mobile']
    }
  ];

  private readonly mockCreators = [
    { id: '1', name: 'Alex Designer', avatar: '/assets/avatars/alex.jpg', projects: 12 },
    { id: '2', name: 'Sarah Developer', avatar: '/assets/avatars/sarah.jpg', projects: 8 },
    { id: '3', name: 'Mike Fullstack', avatar: '/assets/avatars/mike.jpg', projects: 15 },
    { id: '4', name: 'Lisa Educator', avatar: '/assets/avatars/lisa.jpg', projects: 6 },
    { id: '5', name: 'Tom Fitness', avatar: '/assets/avatars/tom.jpg', projects: 4 }
  ];

  private readonly mockTags = [
    'react', 'angular', 'vue', 'typescript', 'javascript',
    'dashboard', 'mobile', 'ecommerce', 'social', 'education',
    'fitness', 'healthcare', 'finance', 'travel', 'gaming'
  ];

  /**
   * Perform global search across all content types using real API
   */
  search(query: string, scope: 'all' | 'projects' | 'community' | 'creators' = 'all'): Observable<GlobalSearchResponse> {
    if (!query || query.length < 2) {
      return of({
        suggestions: [],
        totalResults: 0,
        searchTime: 0
      });
    }

    // Use the community projects service to search via API
    return this.communityService.searchProjects(query, { scope }).pipe(
      map(projects => {
        const suggestions = this.convertProjectsToSuggestions(projects);

        // Add other suggestion types (creators, tags) from mock data
        const additionalSuggestions = this.generateAdditionalSuggestions(query, scope);

        return {
          suggestions: [...suggestions, ...additionalSuggestions],
          totalResults: suggestions.length + additionalSuggestions.length,
          searchTime: 0.2
        };
      }),
      catchError(() => {
        // Fallback to mock data on error
        return of(null).pipe(
          delay(200),
          map(() => {
            const suggestions = this.generateSuggestions(query, scope);
            return {
              suggestions,
              totalResults: suggestions.length,
              searchTime: 0.2
            };
          })
        );
      })
    );
  }

  /**
   * Convert API projects to search suggestions
   */
  private convertProjectsToSuggestions(projects: any[]): SearchSuggestion[] {
    return projects.slice(0, 5).map(project => ({
      id: project.id,
      text: project.title,
      type: 'project',
      icon: project.type === 'wireframe_generation' ? 'fas fa-paint-brush' : 'fas fa-code',
      category: project.type === 'wireframe_generation' ? 'Wireframe' : 'Application',
      metadata: {
        description: project.description,
        creator: project.creator.name,
        rating: project.rating,
        type: project.type
      }
    }));
  }

  /**
   * Generate additional suggestions (creators, tags) from mock data
   */
  private generateAdditionalSuggestions(query: string, scope: string): SearchSuggestion[] {
    const suggestions: SearchSuggestion[] = [];
    const lowerQuery = query.toLowerCase();

    // Search creators (if scope allows)
    if (scope === 'all' || scope === 'creators') {
      const matchingCreators = this.mockCreators.filter(creator =>
        creator.name.toLowerCase().includes(lowerQuery)
      );

      matchingCreators.forEach(creator => {
        suggestions.push({
          id: creator.id,
          text: creator.name,
          type: 'creator',
          icon: 'fas fa-user',
          category: 'Creator',
          metadata: {
            avatar: creator.avatar,
            projects: creator.projects
          }
        });
      });
    }

    // Search tags
    const matchingTags = this.mockTags.filter(tag =>
      tag.toLowerCase().includes(lowerQuery)
    );

    matchingTags.slice(0, 3).forEach(tag => {
      suggestions.push({
        id: tag,
        text: tag,
        type: 'tag',
        icon: 'fas fa-tag',
        category: 'Technology',
        metadata: { tag }
      });
    });

    return suggestions;
  }

  /**
   * Get search suggestions for autocomplete
   */
  getSuggestions(query: string): Observable<SearchSuggestion[]> {
    if (!query || query.length < 2) {
      return of([]);
    }

    return this.search(query).pipe(
      map(response => response.suggestions.slice(0, 8))
    );
  }

  /**
   * Get trending searches
   */
  getTrendingSearches(): Observable<string[]> {
    const trending = [
      'dashboard design',
      'mobile app ui',
      'react components',
      'admin panel',
      'ecommerce template'
    ];
    
    return of(trending);
  }

  /**
   * Get popular tags
   */
  getPopularTags(): Observable<string[]> {
    return of(this.mockTags.slice(0, 10));
  }

  /**
   * Generate search suggestions based on query and scope
   */
  private generateSuggestions(query: string, scope: string): SearchSuggestion[] {
    const suggestions: SearchSuggestion[] = [];
    const lowerQuery = query.toLowerCase();

    // Search projects
    if (scope === 'all' || scope === 'projects' || scope === 'community') {
      const matchingProjects = this.mockProjects.filter(project =>
        project.title.toLowerCase().includes(lowerQuery) ||
        project.description.toLowerCase().includes(lowerQuery) ||
        project.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
      );

      matchingProjects.forEach(project => {
        suggestions.push({
          id: project.id,
          text: project.title,
          type: 'project',
          icon: project.type === 'wireframe_generation' ? 'fas fa-paint-brush' : 'fas fa-code',
          category: project.type === 'wireframe_generation' ? 'Wireframe' : 'Application',
          metadata: {
            description: project.description,
            creator: project.creator,
            rating: project.rating,
            type: project.type
          }
        });
      });
    }

    // Search creators
    if (scope === 'all' || scope === 'creators') {
      const matchingCreators = this.mockCreators.filter(creator =>
        creator.name.toLowerCase().includes(lowerQuery)
      );

      matchingCreators.forEach(creator => {
        suggestions.push({
          id: creator.id,
          text: creator.name,
          type: 'creator',
          icon: 'fas fa-user',
          category: 'Creator',
          metadata: {
            avatar: creator.avatar,
            projects: creator.projects
          }
        });
      });
    }

    // Search tags
    const matchingTags = this.mockTags.filter(tag =>
      tag.toLowerCase().includes(lowerQuery)
    );

    matchingTags.forEach(tag => {
      suggestions.push({
        id: tag,
        text: tag,
        type: 'tag',
        icon: 'fas fa-tag',
        category: 'Technology',
        metadata: {
          tag: tag
        }
      });
    });

    // Add template suggestions
    if (lowerQuery.includes('template') || lowerQuery.includes('starter')) {
      suggestions.push({
        id: 'templates',
        text: 'Browse all templates',
        type: 'template',
        icon: 'fas fa-th-large',
        category: 'Templates',
        metadata: {}
      });
    }

    // Sort suggestions by relevance
    return this.sortSuggestionsByRelevance(suggestions, query);
  }

  /**
   * Sort suggestions by relevance to the search query
   */
  private sortSuggestionsByRelevance(suggestions: SearchSuggestion[], query: string): SearchSuggestion[] {
    const lowerQuery = query.toLowerCase();
    
    return suggestions.sort((a, b) => {
      // Exact matches first
      const aExact = a.text.toLowerCase() === lowerQuery ? 1 : 0;
      const bExact = b.text.toLowerCase() === lowerQuery ? 1 : 0;
      if (aExact !== bExact) return bExact - aExact;

      // Starts with query
      const aStarts = a.text.toLowerCase().startsWith(lowerQuery) ? 1 : 0;
      const bStarts = b.text.toLowerCase().startsWith(lowerQuery) ? 1 : 0;
      if (aStarts !== bStarts) return bStarts - aStarts;

      // Projects have higher priority than other types
      const aProject = a.type === 'project' ? 1 : 0;
      const bProject = b.type === 'project' ? 1 : 0;
      if (aProject !== bProject) return bProject - aProject;

      // Sort by rating for projects
      if (a.type === 'project' && b.type === 'project') {
        const aRating = a.metadata?.rating || 0;
        const bRating = b.metadata?.rating || 0;
        return bRating - aRating;
      }

      // Alphabetical order as fallback
      return a.text.localeCompare(b.text);
    });
  }

  /**
   * Track search analytics (for future implementation)
   */
  trackSearch(query: string, resultCount: number): void {
    // Implementation for search analytics

  }

  /**
   * Get search filters based on current context
   */
  getSearchFilters(): Observable<{
    projectTypes: string[];
    technologies: string[];
    difficulties: string[];
  }> {
    return of({
      projectTypes: ['wireframe_generation', 'app_generation'],
      technologies: this.mockTags,
      difficulties: ['beginner', 'intermediate', 'advanced']
    });
  }
}
