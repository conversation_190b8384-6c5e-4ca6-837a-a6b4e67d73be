.my-agents-container {
  background: #fff;
  border-radius: 12px;
  margin-bottom: 118px;
  padding-left: 32px;
  padding-right: 32px;
}

.my-agents-title {
  color: #14161F;
  text-align: center;
  font-family: Mulish;
  font-size: 48px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  letter-spacing: -0.912px;
  margin: 0;
  padding-bottom: 24px;
}
.my-agent-card {
  display: flex;
  padding: 24px;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 20px;
  flex-shrink: 0;
  margin: 16px;
  height: 248px;
  width: 100%; 

  // Figma specified styling
  border-radius: 12px;
  border: 1px solid rgba(67, 131, 230, 0.10);
  background: #FFF;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.08);

  transition: box-shadow 0.2s;

  &:hover {
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.12);
  }
}

.my-agents-container > .row {
  margin-right: 0 !important;
}

.card-header {
  display: flex;
  align-items: center;
  width: 100%;
  height: 48px;

  .card-icon {
    display: flex;
    width: 48px;
    height: 48px;
    justify-content: center;
    align-items: center;
    aspect-ratio: 1/1;
    border-radius: var(--Border-Radius-circle, 50px);
    background-color: #4383E633;
    margin-right: 0.8rem;
  }

  .card-title {
    color: #3B3F46;
    font-family: Inter;
    font-size: 32px;
    font-style: normal;
    font-weight: 600;
    line-height: 25px;
  }
}

.card-content {
  flex: 1;
  width: 100%;

  .card-description {
    color: #3B3F46;
    font-family: Inter;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
  }
}

.card-footer {
  display: flex;
  gap: 0.5rem;
  width: 100%;

  .card-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.3rem;
    height: 48px;
    font-family: var(--Global-Typography-family-Body, Inter);
    font-size: var(--Typography-Font-size-Text-lg, 20px);
    font-style: normal;
    font-weight: var(--Global-Typography-weight-Medium, 500);
    line-height: var(--Typography-line-height-xs, 24px);
    border-radius: 6px;
    padding: 0 1rem;
    border: 2px solid transparent;
    background: linear-gradient(#fff, #fff) padding-box, linear-gradient(87deg, rgba(250, 167, 74, 1) 8.06%, rgba(67, 131, 230, 1) 73.25%) border-box;
    color: #4383E6;
    cursor: pointer;
    transition: background 0.15s, color 0.15s;
    flex: 1;
    min-width: 0;

    .btn-icon {
      width: 20px;
      height: 20px;
      color: #4383E6;
    }
  }
}