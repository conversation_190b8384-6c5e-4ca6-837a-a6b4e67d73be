.custom-tabs-wrapper {
  display: flex;
  gap: clamp(2px, 0.5vw, 8px);
  .tab-item {
    display: flex;
    flex-direction: column;
    border: none;
    background-color: transparent;
    cursor: pointer;

    &.active {
      .tab-icon-box {
        background: var(--Global-colors-Royal-blue-50, #e9effd);
        border: 1px solid var(--Global-colors-Royal-blue-300, #6c96f2);

        lucide-icon {
          color: #215ad6 !important;
        }

        // Blue filter for prompt asset images when active
        img.tab-png-icon {
          filter: brightness(0) saturate(100%) invert(27%) sepia(51%)
            saturate(2878%) hue-rotate(214deg) brightness(97%) contrast(87%);
        }
      }

      // Keep label black even when active
      .tab-label {
        color: #000000;
        font-weight: 500;
      }
    }

    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .tab-icon-box {
      display: flex;
      align-items: center;
      justify-content: center;
      width: clamp(48px, 15vw, 80px);
      height: clamp(24px, 5vh, 28px);
      border: 1px solid #d1d5db;
      border-radius: 6px;
      margin-bottom: clamp(2px, 0.5vh, 4px);
    }

    .tab-label {
      font-size: clamp(8px, 1.8vw, 10px); // Responsive font size
      font-weight: 500;
      color: #000000;
      text-align: center;
      line-height: 1.2;
      white-space: nowrap; // Prevent text wrapping
      overflow: hidden;
      text-overflow: ellipsis; // Add ellipsis for very long text
      width: 100%;
    }
  }
}

// Enhanced responsive design for various zoom levels and screen sizes
@media (max-width: 1200px) {
  .custom-tabs-wrapper {
    gap: clamp(1px, 0.3vw, 6px);

    .tab-item {
      .tab-icon-box {
        width: clamp(55px, 12vw, 80px);
        height: clamp(22px, 4vh, 26px);
      }

      .tab-label {
        font-size: clamp(7px, 1.6vw, 9px);
      }
    }
  }
}

@media (max-width: 768px) {
  .custom-tabs-wrapper {
    gap: clamp(1px, 0.2vw, 4px);

    .tab-item {
      padding: clamp(2px, 0.3vw, 4px);
      min-height: clamp(36px, 6vh, 44px);

      .tab-icon-box {
        width: clamp(45px, 10vw, 70px);
        height: clamp(20px, 3.5vh, 24px);
        margin-bottom: clamp(1px, 0.3vh, 3px);
      }

      .tab-label {
        font-size: clamp(6px, 1.4vw, 8px);
        color: #000000;
      }
    }
  }
}

@media (max-width: 480px) {
  .custom-tabs-wrapper {
    gap: clamp(0px, 0.1vw, 2px);

    .tab-item {
      padding: clamp(1px, 0.2vw, 3px);
      min-height: clamp(32px, 5vh, 40px);

      .tab-icon-box {
        width: clamp(35px, 8vw, 60px);
        height: clamp(18px, 3vh, 22px);
        margin-bottom: clamp(1px, 0.2vh, 2px);
      }

      .tab-label {
        font-size: clamp(5px, 1.2vw, 7px);
        color: #000000;
      }
    }
  }
}

// Extra small screens and high zoom levels
@media (max-width: 320px) {
  .custom-tabs-wrapper {
    gap: 0;

    .tab-item {
      padding: 1px;
      min-height: 30px;

      .tab-icon-box {
        width: clamp(30px, 6vw, 50px);
        height: clamp(16px, 2.5vh, 20px);
        margin-bottom: 1px;
      }

      .tab-label {
        font-size: clamp(4px, 1vw, 6px);
        color: #000000;
      }
    }
  }
}

// Container queries for better adaptation (if supported)
@container (max-width: 400px) {
  .custom-tabs-wrapper {
    .tab-item {
      .tab-icon-box {
        width: clamp(40px, 8cqw, 65px);
        height: clamp(18px, 3cqh, 24px);
      }

      .tab-label {
        font-size: clamp(6px, 1.5cqw, 8px);
      }
    }
  }
}

// High zoom level adjustments (when viewport becomes very narrow due to zoom)
@media (max-width: 250px) {
  .custom-tabs-wrapper {
    .tab-item {
      .tab-icon-box {
        width: 25px;
        height: 14px;
      }

      .tab-label {
        font-size: 4px;
        line-height: 1;
      }
    }
  }
}
