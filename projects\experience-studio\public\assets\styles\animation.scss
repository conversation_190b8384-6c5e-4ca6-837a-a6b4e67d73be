@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 0.5;
  }
}

@keyframes promptBarBreatheDark {
  0%,
  100% {
    box-shadow:
      0 0 6px rgba(246, 59, 143, 0.2),
      0 0 12px rgba(246, 59, 143, 0.2);
  }
  50% {
    box-shadow:
      0 0 12px rgba(246, 59, 143, 0.4),
      0 0 26px rgba(246, 59, 143, 0.4);
  }
}

@keyframes promptBarBreatheLight {
  0%,
  100% {
    box-shadow:
      0 0 6px rgba(101, 102, 205, 0.2),
      0 0 12px rgba(101, 102, 205, 0.2);
  }
  50% {
    box-shadow:
      0 0 12px rgba(101, 102, 205, 0.4),
      0 0 26px rgba(101, 102, 205, 0.4);
  }
}

@keyframes slideInFromLeft {
  0% {
    transform: translateX(-20%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromRight {
  0% {
    transform: translateX(20%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes shimmer {
  0% {
    background-position: -468px 0;
  }
  100% {
    background-position: 468px 0;
  }
}

@keyframes promptBarProcessingDark {
  0%,
  100% {
    box-shadow:
      0 0 15px rgba(246, 59, 143, 0.6),
      0 0 30px rgba(246, 59, 143, 0.6);
  }
  50% {
    box-shadow:
      0 0 25px rgba(246, 59, 143, 0.9),
      0 0 50px rgba(246, 59, 143, 0.9);
  }
}

@keyframes promptBarProcessingLight {
  0%,
  100% {
    box-shadow:
      0 0 15px rgba(101, 102, 205, 0.6),
      0 0 30px rgba(101, 102, 205, 0.6);
  }
  50% {
    box-shadow:
      0 0 25px rgba(101, 102, 205, 0.9),
      0 0 50px rgba(101, 102, 205, 0.9);
  }
}
