@use '../../../../../../public/assets/styles/mixins' as mixins;

// Header Workspace Styles - Enhanced header for MLO Workspace
.workspace-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: var(--header-bg);
  border-bottom: 1px solid var(--border-light);
  backdrop-filter: blur(8px);
  transition: all 0.3s ease;

  // CSS Custom Properties
  --header-bg: rgba(255, 255, 255, 0.95);
  --header-bg-dark: rgba(33, 38, 45, 0.95);
  --nav-item-hover: #f8f9fa;
  --nav-item-hover-dark: #2d3748;
  --nav-item-active: #8c65f7;
  --search-bg: #f8f9fa;
  --search-bg-dark: #2d3748;

  &[data-theme="dark"] {
    --header-bg: var(--header-bg-dark);
    --nav-item-hover: var(--nav-item-hover-dark);
    --search-bg: var(--search-bg-dark);
  }

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1440px;
    margin: 0 auto;
    padding: 0 1.5rem;
    height: 4rem;
    gap: 2rem;

    .header-left {
      display: flex;
      align-items: center;
      gap: 2rem;
      flex-shrink: 0;

      .logo-section {
        .logo-btn {
          background: transparent;
          border: none;
          cursor: pointer;
          padding: 0.5rem;
          border-radius: 0.5rem;
          transition: all 0.2s ease;

          &:hover {
            background: var(--nav-item-hover);
          }

          .logo {
            display: flex;
            flex-direction: column;
            align-items: center;

            .logo-text {
              font-size: 1.25rem;
              font-weight: 700;
              color: var(--nav-item-active);
              line-height: 1;
            }

            .logo-subtitle {
              font-size: 0.625rem;
              font-weight: 500;
              color: var(--text-secondary);
              line-height: 1;
              margin-top: 0.125rem;
            }
          }
        }
      }

      .main-nav {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .nav-item {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.75rem 1rem;
          background: transparent;
          border: none;
          border-radius: 0.5rem;
          color: var(--text-secondary);
          font-size: 0.875rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
          text-decoration: none;

          &:hover {
            background: var(--nav-item-hover);
            color: var(--text-primary);
          }

          &.active {
            background: rgba(140, 101, 247, 0.1);
            color: var(--nav-item-active);
          }

          i {
            font-size: 1rem;
          }
        }
      }
    }

    .header-center {
      flex: 1;
      max-width: 600px;
      min-width: 300px;
    }

    .header-right {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      flex-shrink: 0;

      .header-action {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 2.5rem;
        height: 2.5rem;
        background: transparent;
        border: none;
        border-radius: 0.5rem;
        color: var(--text-secondary);
        cursor: pointer;
        transition: all 0.2s ease;
        position: relative;

        &:hover {
          background: var(--nav-item-hover);
          color: var(--text-primary);
        }

        &:focus {
          outline: 2px solid var(--nav-item-active);
          outline-offset: 1px;
        }
      }

      .notifications-btn {
        &.has-notifications {
          color: var(--nav-item-active);
        }

        .notification-badge {
          position: absolute;
          top: 0.25rem;
          right: 0.25rem;
          background: #ef4444;
          color: white;
          font-size: 0.625rem;
          font-weight: 600;
          padding: 0.125rem 0.375rem;
          border-radius: 0.75rem;
          min-width: 1rem;
          height: 1rem;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .user-menu-container {
        position: relative;

        .user-menu-btn {
          padding: 0.125rem;

          .user-avatar {
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            overflow: hidden;

            .avatar-image {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
        }

        .user-menu-dropdown {
          position: absolute;
          top: calc(100% + 0.5rem);
          right: 0;
          background: var(--card-bg);
          border: 1px solid var(--border-light);
          border-radius: 0.75rem;
          box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
          min-width: 200px;
          z-index: 1000;
          animation: slideDown 0.2s ease-out;

          .user-info {
            padding: 1rem;
            border-bottom: 1px solid var(--border-light);

            .user-details {
              display: flex;
              flex-direction: column;

              .user-name {
                font-weight: 600;
                color: var(--text-primary);
                font-size: 0.875rem;
              }

              .user-email {
                color: var(--text-secondary);
                font-size: 0.75rem;
                margin-top: 0.125rem;
              }
            }
          }

          .menu-divider {
            height: 1px;
            background: var(--border-light);
            margin: 0.5rem 0;
          }

          .menu-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            width: 100%;
            padding: 0.75rem 1rem;
            background: transparent;
            border: none;
            color: var(--text-primary);
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: left;

            &:hover {
              background: var(--nav-item-hover);
            }

            &.logout {
              color: #ef4444;

              &:hover {
                background: rgba(239, 68, 68, 0.1);
              }
            }

            i {
              width: 1rem;
              text-align: center;
            }
          }
        }
      }
    }
  }

  .mobile-nav {
    display: none;
    background: var(--header-bg);
    border-top: 1px solid var(--border-light);
    padding: 0.5rem 1rem;
    gap: 0.5rem;

    &.visible {
      display: flex;
    }

    .mobile-nav-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 0.25rem;
      padding: 0.75rem 1rem;
      background: transparent;
      border: none;
      border-radius: 0.5rem;
      color: var(--text-secondary);
      font-size: 0.75rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      flex: 1;

      &:hover {
        background: var(--nav-item-hover);
        color: var(--text-primary);
      }

      &.active {
        background: rgba(140, 101, 247, 0.1);
        color: var(--nav-item-active);
      }

      i {
        font-size: 1.25rem;
      }
    }
  }
}

// Animations
@keyframes slideDown {
  0% {
    opacity: 0;
    transform: translateY(-0.5rem);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .workspace-header {
    .header-content {
      padding: 0 1rem;
      height: 3.5rem;
      gap: 1rem;

      .header-left {
        gap: 1rem;

        .main-nav {
          display: none;
        }
      }

      .header-center {
        min-width: 200px;
      }

      .header-right {
        gap: 0.5rem;

        .header-action {
          width: 2.25rem;
          height: 2.25rem;
        }
      }
    }

    .mobile-nav {
      display: flex;
    }
  }
}

@media (max-width: 480px) {
  .workspace-header {
    .header-content {
      padding: 0 0.75rem;
      gap: 0.75rem;

      .header-center {
        min-width: 150px;
      }
    }
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .workspace-header {
    border-bottom-width: 2px;

    .header-content .header-left .main-nav .nav-item {
      border: 1px solid transparent;

      &.active {
        border-color: var(--nav-item-active);
      }
    }
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .workspace-header {
    * {
      transition: none !important;
      animation: none !important;
    }
  }
}
