<div class="layout">
    <!-- Left Pane -->
    <div class="left-pane" [class.collapsed]="isLeftCollapsed">
      <div class="left-header">
        <span class="pane-title" *ngIf="!isLeftCollapsed">{{ leftPaneTitle }}</span>
        <lucide-icon
          [name]="isLeftCollapsed ? 'panel-right' : 'panel-left'"
          class="icon"
          (click)="toggleLeftPane()"
        ></lucide-icon>
      </div>
      <div class="left-content" *ngIf="!isLeftCollapsed">
        <ng-content select="[left]"></ng-content>
      </div>
    </div>
  
    <!-- Center Pane -->
    <div class="center-pane" [class.full-width]="!showRightPane">
      <div class="center-header" *ngIf="centerPaneTitle">
        <span class="pane-title">{{ centerPaneTitle }}</span>
      </div>
      <ng-content select="[center]"></ng-content>
    </div>
  
    <!-- Right Pane (Optional) -->
    <div class="right-pane" *ngIf="showRightPane">
      <div class="right-header">
        <span class="pane-title">{{ rightPaneTitle }}</span>
      </div>
      <ng-content select="[right]"></ng-content>
    </div>
  </div>
  