import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { NgIf } from '@angular/common';

type IconColor = 'action' | 'danger' | 'disable' | 'neutralIcon' | 'success' | 'warning' | 'whiteIcon' | 'blue' | 'header' | 'pink'|'vscodeblue'|'';

/**
 * Type for icon names that follow the pattern: awe_name_filled
 * All icons must start with 'awe_' and end with '_filled'
 */

@Component({
  selector: 'exp-icons',
  imports: [NgIf],
  templateUrl: './icons.component.html',
  styleUrls: ['./icons.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true
})
export class IconsComponent implements OnChanges {
  @Input() iconName  = 'awe_arrow_rightward_filled';
  @Input() color: string = ''; // Accepts hex or any valid color format
  @Input() disabled = false;
  @Input() iconColor: IconColor = '';

  svgContent: SafeHtml = '';

  private readonly tokenMap: Record<IconColor, string> = {
    'action': 'var(--icons-action)',
    'danger': 'var(--icons-danger)',
    'disable': 'var(--icons-disable)',
    'neutralIcon': 'var(--icons-neutral-icon)',
    'success': 'var(--icons-success)',
    'warning': 'var(--icons-warning)',
    'whiteIcon': 'var(--icons-white-icon)',
    'blue': 'var(--icons-blue)',
    'pink': 'var(--icons-pink)',
    'vscodeblue':'var(--primary-color)',
    'header': '#666D99',
    '': 'black'
  };

  constructor(
    private readonly cdr: ChangeDetectorRef,
    private readonly http: HttpClient,
    private readonly sanitizer: DomSanitizer
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['iconName']) {
      this.loadSvgContent();
    }
  }

  private loadSvgContent(): void {
    if (!this.iconName) return;

    this.http.get(`assets/icons/${this.iconName}.svg`, { responseType: 'text' })
      .subscribe({
        next: (response: string) => {
          // Get final color: User-provided color, iconColor from token map, or fallback to 'black'
          const finalColor = this.color || this.getColorFromToken() || 'black';

          // Ensure the color is either a valid hex or a valid CSS color
          const validColor = this.isValidHexColor(finalColor) || this.isValidCssColor(finalColor) 
            ? finalColor 
            : 'black'; // Fallback to black if the color is not valid

          const modifiedSvg = response.replace(/fill=".*?"/g, `fill="${validColor}"`);
          this.svgContent = this.sanitizer.bypassSecurityTrustHtml(modifiedSvg);
          this.cdr.markForCheck();
        },
        error: () => {
          // Handle error silently - icon will not display if SVG fails to load
        }
      });
  }

  private getColorFromToken(): string {
    return this.tokenMap[this.iconColor];
  }

  // Method to check if the provided string is a valid hex color
  private isValidHexColor(color: string): boolean {
    // Hex color validation (either 6 or 3 hex digits with optional #)
    return /^#([0-9A-Fa-f]{3}){1,2}$/.test(color);
  }

  // Method to check if the provided string is a valid CSS color
  private isValidCssColor(color: string): boolean {
    const s = new Option().style;
    s.color = color;
    return s.color !== '';
  }

  onKeydown(event: KeyboardEvent): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.handleIconClick();
    }
  }
  
  private handleIconClick(): void {
    if (this.disabled) return;

  }
}
