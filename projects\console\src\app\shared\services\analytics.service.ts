import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class AnalyticsService {
  private apiServiceUrl = environment?.consoleApi;

  constructor(private http: HttpClient) { }
  private getHeaders(): { headers: HttpHeaders } {
    return {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
      })
    };
  }

  private getDownloadHeaders(): HttpHeaders {
    return new HttpHeaders({
      'Content-Type': 'application/json',
    });
  }

  public getAllAnalyticsData(payload: any): Observable<any> {
    const fromDate = payload['fromDate'];
    const toDate = payload['toDate'];
    const url = `${this.apiServiceUrl}/ava/force/analytics?dateStart=${fromDate}&dateEnd=${toDate}`;

    return this.http.get(url, this.getHeaders());
  }

  // Usecase Analytics Methods
  public totalRequestCount(payload: any): Observable<any> {
    const fromDate = payload['fromDate'];
    const toDate = payload['toDate'];
    const headers = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json'
      })
    };
    const url = `${this.apiServiceUrl}/ava/force/analytics?dateStart=${fromDate}&dateEnd=${toDate}`;

    const result = {
      totalBugs: 0,
      totalUnitTest: 0,
      totalStory: 0,
      totalCodeOptimization: 0
    };

    return this.http.get(url, headers).pipe(
      map((response: any) => {
        let totalStoryOrEpic = 0;
        if (response?.useCaseLevelAnalytics) {
          response.useCaseLevelAnalytics.forEach((data: any) => {
            switch (data.useCaseCode) {
              case 'FIND_BUG':
                result.totalBugs = data.count;
                break;
              case 'GENERATE_UNIT_TESTS':
                result.totalUnitTest = data.count;
                break;
              case 'STORY_OR_EPIC':
              case 'CREATE_STORY':
                totalStoryOrEpic += data.count;
                break;
              case 'OPTIMIZE_CODE':
                result.totalCodeOptimization = data.count;
                break;
            }
          });
        }
        result.totalStory = totalStoryOrEpic;
        return result;
      })
    );
  }

  public getLinesOfCodeProcessed(payload: any): Observable<any> {
    const fromDate = payload['fromDate'];
    const toDate = payload['toDate'];
    const headers = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
      })
    };
    const url = `${this.apiServiceUrl}/ava/force/analytics?dateStart=${fromDate}&dateEnd=${toDate}`;

    const result: any = {
      categories: [],
      ySeries: [],
    };

    return this.http.get(url, headers).pipe(
      map((response: any) => {
        if (response?.linesOfCodeAnalytics) {
          response.linesOfCodeAnalytics.forEach((data: any) => {
            result.categories.push(data.date);
            result.ySeries.push(data.count);
          });
        }
        return result;
      })
    );
  }

  public getTopUseCases(payload: any): Observable<any> {
    const fromDate = payload['fromDate'];
    const toDate = payload['toDate'];
    const headers = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
      })
    };
    const url = `${this.apiServiceUrl}/ava/force/analytics?dateStart=${fromDate}&dateEnd=${toDate}`;

    const result: any = {
      categories: [],
      ySeries: [],
    };

    return this.http.get(url, headers).pipe(
      map((response: any) => {
        if (response?.useCaseLevelAnalytics) {
          response.useCaseLevelAnalytics.slice(0, 5).forEach((data: any) => {
            result.categories.push(data.useCaseCode);
            result.ySeries.push(data.count);
          });
        }
        return result;
      })
    );
  }

  public getTotalNumberOfRequests(payload: any): Observable<any> {
    const fromDate = payload['fromDate'];
    const toDate = payload['toDate'];
    const headers = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
      })
    };
    const url = `${this.apiServiceUrl}/ava/force/analytics?dateStart=${fromDate}&dateEnd=${toDate}`;

    const result: any = {
      categories: [],
      ySeries: [],
    };

    return this.http.get(url, headers).pipe(
      map((response: any) => {
        if (response?.numberOfRequestAnalytics) {
          response.numberOfRequestAnalytics.forEach((data: any) => {
            result.categories.push(data.date);
            result.ySeries.push(data.requestCount);
          });
        }
        return result;
      })
    );
  }

  public getTopLanguages(payload: any): Observable<any> {
    const fromDate = payload['fromDate'];
    const toDate = payload['toDate'];
    const headers = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
      })
    };
    const url = `${this.apiServiceUrl}/ava/force/analytics?dateStart=${fromDate}&dateEnd=${toDate}`;

    const result: any = {
      categories: [],
      ySeries: [],
    };

    return this.http.get(url, headers).pipe(
      map((response: any) => {
        if (response?.programmingLanguageAnalytics) {
          response.programmingLanguageAnalytics.slice(0, 5).forEach((data: any) => {
            result.categories.push(data.programmingLanguage);
            result.ySeries.push(data.count);
          });
        }
        return result;
      })
    );
  }

  public getUserResponse(payload: any): Observable<any> {
    const fromDate = payload['fromDate'];
    const toDate = payload['toDate'];
    const headers = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
      })
    };
    const url = `${this.apiServiceUrl}/ava/force/analytics?dateStart=${fromDate}&dateEnd=${toDate}`;

    const result: any = {
      categories: [],
      ySeries: [],
    };

    return this.http.get(url, headers).pipe(
      map((response: any) => {
        let zeroCount = 0;
        if (response?.userResponseAnalytics) {
          response.userResponseAnalytics.forEach((data: any) => {
            result.categories.push(data.response);
            result.ySeries.push([data.response, data.percentage]);
            if (data.percentage === 0) {
              zeroCount++;
            }
          });
        }

        // Return empty result if all percentages are 0
        if (zeroCount === 3) {
          return { categories: [], ySeries: [] };
        }
        return result;
      })
    );
  }

  public getUserUsageAnalytics(payload: any): Observable<any> {
    const fromDate = payload['fromDate'];
    const toDate = payload['toDate'];
    const headers = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
      })
    };
    const url = `${this.apiServiceUrl}/ava/force/user/analytics?dateStart=${fromDate}&dateEnd=${toDate}`;
    return this.http.get(url, headers);
  }

  public getUserActivity(payload: any): Observable<any> {
    const fromDate = payload['fromDate'];
    const toDate = payload['toDate'];
    const headers = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
      })
    };
    const url = `${this.apiServiceUrl}/ava/force/useractivity/analytics?dateStart=${fromDate}&dateEnd=${toDate}`;
    return this.http.get(url, headers);
  }

  public getResponseTime(payload: any): Observable<any> {
    const fromDate = payload['fromDate'];
    const toDate = payload['toDate'];
    const headers = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
      })
    };
    const url = `${this.apiServiceUrl}/ava/force/apiresponse/analytics?dateStart=${fromDate}&dateEnd=${toDate}`;
    return this.http.get(url, headers);
  }

  // Agent Analytics Methods
  public getAgenticAIAnalytics(payload: any): Observable<any> {
    const fromDate = payload['fromDate'];
    const toDate = payload['toDate'];
    const headers = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
      })
    };
    const url = `${this.apiServiceUrl}/ava/force/analytics/agenticAI?dateStart=${fromDate}&dateEnd=${toDate}`;

    return this.http.get(url, headers).pipe(
      map((response: any) => {
        const toolUsage = (response.toolUsage || []).filter((tool: any) =>
          tool.usageCount !== null && tool.usageCount > 0
        );
        const topAgents = (response.topAgents || []).slice(0, 5);
        const validAgents = (response.agentCreated || []).filter((agent: any) =>
          agent.teamName && agent.usageCount > 0
        );
        const top3AgentsCreated = validAgents.slice(0, 3);

        return {
          totalAgentsCreated: response.totalAgentsCreated,
          totalAgentsReused: response.totalAgentsReused,
          totalTools: response.totalTools,
          studioUsage: response.studioUsage,
          topAgents: topAgents,
          agentCreated: top3AgentsCreated,
          agentMetrics: response.agentMetrics,
          toolAnalytics: response.toolAnalytics,
          toolUsage: toolUsage,
          adoptionRate: response.adoptionRate,
          userConsumption: response.userConsumption,
          userActivityStats: response.userActivityStats,
        };
      })
    );
  }

  // Filter Analytics Data
  public filterAnalyticsData(payload: any, type: string, listOfNames: string[]): Observable<any> {
    const fromDate = payload['fromDate'];
    const toDate = payload['toDate'];
    const headers = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
      })
    };

    let listParams = '';
    listOfNames.forEach(name => {
      listParams += `&listOfNames=${encodeURIComponent(name)}`;
    });

    const url = `${this.apiServiceUrl}/ava/force/analytics/usecaseoragent?dateStart=${fromDate}&dateEnd=${toDate}&type=${type}${listParams}`;
    return this.http.get(url, headers);
  }

  // Download Methods
  public downloadChartExcel(date: any, analyticsName: string, fileFormat: string): void {
    const fromDate = date['fromDate'];
    const toDate = date['toDate'];

    const headers = this.getDownloadHeaders();
    const url = `${this.apiServiceUrl}/ava/force/analytics/download?dateStart=${fromDate}&dateEnd=${toDate}&analyticsName=${analyticsName}&fileFormat=${fileFormat}`;

    this.http.get(url, { headers, responseType: 'blob' }).subscribe({
      next: (blob) => {
        const fileName = `${analyticsName}.xlsx`;
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = fileName;
        link.click();
        window.URL.revokeObjectURL(link.href);
      },
      error: (error) => {
        console.error('Error downloading chart excel:', error);
      }
    });
  }

  public downloadDump(date: any): void {
    const fromDate = date['fromDate'];
    const toDate = date['toDate'];

    const headers = this.getDownloadHeaders();
    const url = `${this.apiServiceUrl}/ava/force/analytics/dump/data/download?dateStart=${fromDate}&dateEnd=${toDate}`;

    this.http.get(url, { headers, responseType: 'blob' }).subscribe({
      next: (blob) => {
        const fileName = 'AnalyticsDump.xlsx';
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = fileName;
        link.click();
        window.URL.revokeObjectURL(link.href);
      },
      error: (error) => {
        console.error('Error downloading data dump:', error);
      }
    });
  }

  public downloadUsecaseChartExcel(date: any, analyticsName: string, fileFormat: string, usecases: string): void {
    const fromDate = date['fromDate'];
    const toDate = date['toDate'];

    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
    });

    const url = `${this.apiServiceUrl}/ava/force/analytics/download?dateStart=${fromDate}&dateEnd=${toDate}&analyticsName=${analyticsName}&fileFormat=${fileFormat}&usecases=${usecases}`;

    this.http.get(url, { headers, responseType: 'blob' }).subscribe({
      next: (blob) => {
        const fileName = `${analyticsName}.xlsx`;
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = fileName;
        link.click();
        window.URL.revokeObjectURL(link.href);
      },
      error: (error) => {
        console.error('Error downloading the file', error);
      }
    });
  }

  public downloadAgenticAIExcel(date: any, analyticsName: string, fileFormat: string): void {
    const fromDate = date['fromDate'];
    const toDate = date['toDate'];

    const headers = this.getDownloadHeaders();
    const url = `${this.apiServiceUrl}/ava/force/analytics/download/agenticAI?dateStart=${fromDate}&dateEnd=${toDate}&analytics_name=${analyticsName}&fileFormat=${fileFormat}`;

    this.http.get(url, { headers, responseType: 'blob' }).subscribe({
      next: (blob) => {
        const fileName = `${analyticsName}.xlsx`;
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = fileName;
        link.click();
        window.URL.revokeObjectURL(link.href);
      },
      error: (error) => {
        console.error('Error downloading agenticAI excel:', error);
      }
    });
  }
}
