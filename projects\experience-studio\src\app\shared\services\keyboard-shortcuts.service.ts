import { Injectable, inject, DestroyRef } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { fromEvent } from 'rxjs';
import { filter } from 'rxjs/operators';

export interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  altKey?: boolean;
  shiftKey?: boolean;
  metaKey?: boolean;
  action: () => void;
  description: string;
  category?: string;
}

/**
 * Service for managing keyboard shortcuts across the application
 * Provides a centralized way to register and handle keyboard shortcuts
 */
@Injectable({
  providedIn: 'root'
})
export class KeyboardShortcutsService {
  private readonly destroyRef = inject(DestroyRef);
  private readonly shortcuts = new Map<string, KeyboardShortcut>();
  private isEnabled = true;
  
  constructor() {
    this.setupGlobalKeyListener();
  }
  
  /**
   * Register a keyboard shortcut
   */
  registerShortcut(shortcut: KeyboardShortcut): void {
    const key = this.createShortcutKey(shortcut);
    this.shortcuts.set(key, shortcut);
  }
  
  /**
   * Unregister a keyboard shortcut
   */
  unregisterShortcut(shortcut: Partial<KeyboardShortcut>): void {
    const key = this.createShortcutKey(shortcut);
    this.shortcuts.delete(key);
  }
  
  /**
   * Get all registered shortcuts
   */
  getShortcuts(): KeyboardShortcut[] {
    return Array.from(this.shortcuts.values());
  }
  
  /**
   * Get shortcuts by category
   */
  getShortcutsByCategory(category: string): KeyboardShortcut[] {
    return this.getShortcuts().filter(shortcut => shortcut.category === category);
  }
  
  /**
   * Enable or disable keyboard shortcuts
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }
  
  /**
   * Check if shortcuts are enabled
   */
  isShortcutsEnabled(): boolean {
    return this.isEnabled;
  }
  
  /**
   * Register common project shortcuts
   */
  registerProjectShortcuts(callbacks: {
    focusSearch?: () => void;
    toggleFilters?: () => void;
    clearFilters?: () => void;
    switchToRecent?: () => void;
    switchToAll?: () => void;
    refreshProjects?: () => void;
  }): void {
    if (callbacks.focusSearch) {
      this.registerShortcut({
        key: '/',
        action: callbacks.focusSearch,
        description: 'Focus search input',
        category: 'projects'
      });
      
      this.registerShortcut({
        key: 'f',
        ctrlKey: true,
        action: callbacks.focusSearch,
        description: 'Focus search input',
        category: 'projects'
      });
    }
    
    if (callbacks.toggleFilters) {
      this.registerShortcut({
        key: 'f',
        altKey: true,
        action: callbacks.toggleFilters,
        description: 'Toggle filters panel',
        category: 'projects'
      });
    }
    
    if (callbacks.clearFilters) {
      this.registerShortcut({
        key: 'c',
        altKey: true,
        action: callbacks.clearFilters,
        description: 'Clear all filters',
        category: 'projects'
      });
    }
    
    if (callbacks.switchToRecent) {
      this.registerShortcut({
        key: '1',
        altKey: true,
        action: callbacks.switchToRecent,
        description: 'Switch to recent projects',
        category: 'projects'
      });
    }
    
    if (callbacks.switchToAll) {
      this.registerShortcut({
        key: '2',
        altKey: true,
        action: callbacks.switchToAll,
        description: 'Switch to all projects',
        category: 'projects'
      });
    }
    
    if (callbacks.refreshProjects) {
      this.registerShortcut({
        key: 'r',
        ctrlKey: true,
        action: () => {
          callbacks.refreshProjects!();
        },
        description: 'Refresh projects',
        category: 'projects'
      });
    }
  }
  
  /**
   * Unregister all project shortcuts
   */
  unregisterProjectShortcuts(): void {
    const projectShortcuts = this.getShortcutsByCategory('projects');
    projectShortcuts.forEach(shortcut => this.unregisterShortcut(shortcut));
  }
  
  /**
   * Create a unique key for a shortcut
   */
  private createShortcutKey(shortcut: Partial<KeyboardShortcut>): string {
    const parts = [];
    if (shortcut.ctrlKey) parts.push('ctrl');
    if (shortcut.altKey) parts.push('alt');
    if (shortcut.shiftKey) parts.push('shift');
    if (shortcut.metaKey) parts.push('meta');
    parts.push(shortcut.key?.toLowerCase() || '');
    return parts.join('+');
  }
  
  /**
   * Setup global keyboard event listener
   */
  private setupGlobalKeyListener(): void {
    fromEvent<KeyboardEvent>(document, 'keydown')
      .pipe(
        filter(() => this.isEnabled),
        filter(event => !this.isInputFocused(event)),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(event => {
        const shortcutKey = this.createShortcutKey({
          key: event.key,
          ctrlKey: event.ctrlKey,
          altKey: event.altKey,
          shiftKey: event.shiftKey,
          metaKey: event.metaKey
        });
        
        const shortcut = this.shortcuts.get(shortcutKey);
        if (shortcut) {
          event.preventDefault();
          event.stopPropagation();
          shortcut.action();
        }
      });
  }
  
  /**
   * Check if an input element is currently focused
   */
  private isInputFocused(event: KeyboardEvent): boolean {
    const target = event.target as HTMLElement;
    if (!target) return false;
    
    const tagName = target.tagName.toLowerCase();
    const isInput = tagName === 'input' || tagName === 'textarea' || tagName === 'select';
    const isContentEditable = target.contentEditable === 'true';
    
    return isInput || isContentEditable;
  }
  
  /**
   * Format shortcut for display
   */
  formatShortcut(shortcut: KeyboardShortcut): string {
    const parts = [];
    
    if (shortcut.ctrlKey) parts.push('Ctrl');
    if (shortcut.altKey) parts.push('Alt');
    if (shortcut.shiftKey) parts.push('Shift');
    if (shortcut.metaKey) parts.push('Cmd');
    
    // Format key name
    let keyName = shortcut.key;
    if (keyName === ' ') keyName = 'Space';
    else if (keyName === 'ArrowUp') keyName = '↑';
    else if (keyName === 'ArrowDown') keyName = '↓';
    else if (keyName === 'ArrowLeft') keyName = '←';
    else if (keyName === 'ArrowRight') keyName = '→';
    else if (keyName === 'Enter') keyName = 'Enter';
    else if (keyName === 'Escape') keyName = 'Esc';
    else keyName = keyName.toUpperCase();
    
    parts.push(keyName);
    
    return parts.join(' + ');
  }
}
