import { Component, inject, signal } from '@angular/core';
import { UserManagementService } from '../../services/user-management.service';
import { ButtonComponent } from '@ava/play-comp-library';
import { Router } from '@angular/router';
import { TableGridComponent } from 'projects/console/src/app/shared/components/table-grid/table-grid.component';
import { columnDefs } from 'projects/console/src/app/shared/components/table-grid/model/table-grid.model';
import { AdminManagementCellComponent } from './renderer/action-management-renderer.component';
import { DrawerService } from 'projects/console/src/app/shared/services/drawer/drawer.service';
import { UserManagementPreviewPanelComponent } from './renderer/user-management-preview-panel.component';

@Component({
  selector: 'app-view-user-management',
  imports: [ButtonComponent, TableGridComponent],
  templateUrl: './view-user-management.component.html',
  styleUrl: './view-user-management.component.scss'
})
export class ViewUserManagementComponent {
  readonly columns: columnDefs[] = [
    {
      header: 'Name',
      field: 'userName',
      cellRenderer: AdminManagementCellComponent,
      cellRendererParams: { context: { componentParent: this } },
    },
    { header: 'Email', field: 'email' },
    { header: 'Access', field: 'roles' },
    { header: 'Added On', field: 'createdAt' },
    { header: 'Authorized By', field: 'authorizedBy' },
    {
      header: 'Action',
      field: 'action',
      cellRenderer: AdminManagementCellComponent,
      maxWidth: 120,
      cellRendererParams: { context: { componentParent: this } },
    },
  ];
  rows = signal<any[]>([]);


 private readonly userManagementService = inject(UserManagementService);
 private readonly router = inject(Router);
 private readonly drawerService = inject(DrawerService);

  ngOnInit(): void {
    this.getUserList();
  }

  getUserList() {
    this.userManagementService.getAllUsers().subscribe({
      next: (res: any) => {
        this.rows.set(res.userMgmtResponse || []);
      },
      error: (e) => console.error('Failed to load users', e)
    })
  }

  addNewUser() {
    this.router.navigate(['manage/admin-management/add-user']);
  }

  openPreviewPanel(data: any) {
    this.drawerService.open(UserManagementPreviewPanelComponent, {
      metaData: data,
      closePreview: (() => this.drawerService.clear()) as any,
    });
  }

  deleteUser(value: string) {
    console.log('deleted successfully ->', value);
  }
}
