:root {
  // Light Theme Variables
  min-height: 100vh;
  width: 100%;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  // background-image: url('../svgs/main-bg-light.svg');
  
  --background-color: #F5F5F7;
  --body-text-color: #000000;
  --main-title: linear-gradient(90deg, #6566cd 24%, #f63b8f 68%);
  --main-subtitle: #666d99;

  /* Scrollbar Variables - Light Theme */
  --scrollbar-thumb: rgba(101, 102, 205, 0.3);
  --scrollbar-thumb-hover: rgba(101, 102, 205, 0.5);
  --chat-window-card-bg-color: #f8f8ff;
  --chat-window-card-border: transparent;
  --modal-text-color:#14161f;

// FEATURE LIST COMPONENT// FEATURE LIST COMPONENT
--feature-card-header-bg: linear-gradient(93deg, #7c3aed -0.63%, #2c62b9 102.46%); 
--feature-card-header-tital-color: #fff;
--feature-card-header-action-color: #2f68c5;
--feature-card-header-action-bg: #fff;
--feature-card-border-redius: 0.5 rem;
--feature-card-color: #333333;
--feature-card-bg: #fff;
--feature-card-title: #0047BC;
--feature-card-desc-color: #666666;
--feature-card-action-btn-bg: #fff;
--feature-card-action-btn-color: #212529;
--feature-cared-add-card-btn-bg: #fff;
--feature-cared-add-card-btn-border: #6D6DF6;
--feature-cared-add-card-btn-color: #495057;
--feature-tag-bg: #F1F1FA;
--feature-tag-color: #6566CD;

// ROADMAP CARD COMPONENT
--roadmap-card-header-bg: #fff;
--roadmap-card-header-title-color: #0d0c0c;
--roadmap-card-header-action-color: #2f68c5;
--roadmap-card-header-action-bg: #fff;
--roadmap-card-border-redius: 0.5rem;
--roadmap-card-color: #333333;
--roadmap-card-bg: #fff;
--roadmap-card-title: #0047BC;
--roadmap-card-desc-color: #666666;
--roadmap-card-action-btn-bg: #fff;
--roadmap-card-action-btn-color: #212529;
--roadmap-card-add-card-btn-bg: #fff;
--roadmap-card-add-card-btn-border: #6D6DF6;
--roadmap-card-add-card-btn-color: #495057;
--roadmap-card-tag-bg: #F1F1FA;
--roadmap-card-tag-color: #6566CD;

// Priority Colors for Roadmap Cards
--priority-low-color: #D58D49;
--priority-low-bg: rgba(223, 168, 116, 0.20);
--priority-medium-color: #68B266;
--priority-medium-bg: rgba(131, 194, 157, 0.20);
--priority-high-color: #D58D49;
--priority-high-bg: rgba(216, 114, 125, 0.10);

// FEATURE LIST COMPONENT// FEATURE LIST COMPONENT
--swot-card-header-tital-color: #fff;
--swot-card-header-bg: linear-gradient(295deg, #ffadc8 9.05%, #437ff6 50.58%);
--swot-card-header-action-color: #DB2877;;
--swot-card-header-action-bg: #fff;
--swot-card-border-redius: 0.5 rem;
--swot-card-color: #333333;
--swot-card-bg: #fff;
--swot-card-title: #0047BC;
--swot-card-desc-color: #666666;
--swot-card-action-btn-bg: #fff;
--swot-card-action-btn-color: #212529;
--swot-cared-add-card-btn-bg: #fff;
--swot-cared-add-card-btn-border: #6D6DF6;
--swot-cared-add-card-btn-color: #495057;
--swot-tag-bg: #F1F1FA;
--swot-tag-color: #6566CD;





 // page Header
  --page-header-bg: linear-gradient(90deg, #8b5cf6 0%, #3c82f6 100%); 


}

.dark-theme {
  // Dark Theme Variables
  overflow: auto;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  // background-image: url('../svgs/main-bg.svg');
  // --background-gradient-color:linear-gradient(to bottom right, #a855f7, #3b82f6, #ec4899, #f97316);
  --background-color: #121212;
 

  /* Scrollbar Variables - Dark Theme */
  --scrollbar-thumb: rgba(246, 59, 143, 0.4);
  --scrollbar-thumb-hover: rgba(246, 59, 143, 0.6);
  --chat-window-card-bg-color: linear-gradient(
    102.14deg,
    rgba(20, 27, 31, 0.24) 2.05%,
    rgba(20, 27, 31, 0.24) 100%
  );
}
