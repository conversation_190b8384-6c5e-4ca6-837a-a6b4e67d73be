import { Component, inject } from '@angular/core';
import { UserManagementService } from '../../services/user-management.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-add-user',
  imports: [],
  templateUrl: './add-user.component.html',
  styleUrl: './add-user.component.scss'
})
export class AddUserComponent {
private userManagementService = inject(UserManagementService);
 private router = inject(Router);
 

  ngOnInit(): void {
    this.getRoleList();
    
  }

  getRoleList() {
    this.userManagementService.getAllRoles().subscribe({
      next: (userMgmtResponse: any) => {
        console.log(userMgmtResponse)
      },
      error: (e) => console.error(e)
    })
  }
}
