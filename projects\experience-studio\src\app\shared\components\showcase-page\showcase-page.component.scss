@use '../../../../../../public/assets/styles/mixins' as mixins;

// Showcase Page Component Styles - Community projects discovery
.showcase-page {
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 4rem);
  background: var(--workspace-bg);

  // CSS Custom Properties
  --showcase-header-bg: var(--card-bg);
  --category-tab-bg: rgba(140, 101, 247, 0.1);
  --category-tab-active: var(--primary-purple);
  --filters-bg: var(--card-bg);
  --projects-bg: var(--workspace-bg);
  --activity-bg: var(--card-bg);

  .showcase-header {
    background: var(--showcase-header-bg);
    border-bottom: 1px solid var(--border-light);
    padding: 2rem 0 0;

    .header-content {
      max-width: 1440px;
      margin: 0 auto;
      padding: 0 1.5rem 1.5rem;
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      gap: 2rem;

      .page-title {
        flex: 1;

        h1 {
          font-size: 2rem;
          font-weight: 700;
          color: var(--text-primary);
          margin: 0 0 0.5rem;
          line-height: 1.2;
        }

        .page-subtitle {
          font-size: 1rem;
          color: var(--text-secondary);
          margin: 0;
          line-height: 1.5;
        }
      }

      .header-stats {
        display: flex;
        gap: 2rem;
        flex-shrink: 0;

        .stat-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;

          .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-purple);
            line-height: 1;
          }

          .stat-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-top: 0.25rem;
            line-height: 1;
          }
        }
      }
    }

    .category-tabs {
      max-width: 1440px;
      margin: 0 auto;
      padding: 0 1.5rem;
      display: flex;
      gap: 0.5rem;
      overflow-x: auto;
      scrollbar-width: none;
      -ms-overflow-style: none;

      &::-webkit-scrollbar {
        display: none;
      }

      .category-tab {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.25rem;
        background: transparent;
        border: 1px solid var(--border-light);
        border-radius: 2rem;
        color: var(--text-secondary);
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        white-space: nowrap;
        flex-shrink: 0;

        &:hover {
          background: var(--category-tab-bg);
          border-color: var(--primary-purple);
          color: var(--text-primary);
        }

        &.active {
          background: var(--category-tab-active);
          border-color: var(--category-tab-active);
          color: white;
        }

        i {
          font-size: 1rem;
        }

        .category-count {
          font-size: 0.75rem;
          opacity: 0.8;
        }
      }
    }
  }

  .showcase-content {
    flex: 1;
    display: grid;
    grid-template-columns: 280px 1fr 320px;
    gap: 1.5rem;
    max-width: 1440px;
    margin: 0 auto;
    padding: 1.5rem;
    align-items: start;

    .filters-sidebar {
      background: var(--filters-bg);
      border: 1px solid var(--border-light);
      border-radius: 0.75rem;
      padding: 1.5rem;
      position: sticky;
      top: 5.5rem;
      max-height: calc(100vh - 7rem);
      overflow-y: auto;
      transition: all 0.3s ease;

      &.collapsed {
        width: 3rem;
        padding: 1rem 0.5rem;

        .sidebar-header h3 {
          display: none;
        }
      }

      .sidebar-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1.5rem;

        h3 {
          font-size: 1.125rem;
          font-weight: 600;
          color: var(--text-primary);
          margin: 0;
        }

        .toggle-filters {
          background: transparent;
          border: none;
          color: var(--text-secondary);
          cursor: pointer;
          padding: 0.25rem;
          border-radius: 0.25rem;
          transition: all 0.2s ease;

          &:hover {
            background: var(--nav-item-hover);
            color: var(--text-primary);
          }
        }
      }
    }

    .projects-main {
      background: var(--projects-bg);
      min-height: 600px;

      .results-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1.5rem;
        gap: 1rem;

        .results-info {
          display: flex;
          align-items: center;
          gap: 1rem;

          .results-count {
            font-size: 0.875rem;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
          }

          .clear-filters-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 0.75rem;
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid #ef4444;
            border-radius: 0.5rem;
            color: #ef4444;
            font-size: 0.75rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
              background: #ef4444;
              color: white;
            }

            &.primary {
              background: var(--primary-purple);
              border-color: var(--primary-purple);
              color: white;

              &:hover {
                background: #7c3aed;
              }
            }
          }
        }

        .results-controls {
          display: flex;
          align-items: center;
          gap: 1rem;

          .view-toggle {
            display: flex;
            background: var(--card-bg);
            border: 1px solid var(--border-light);
            border-radius: 0.5rem;
            overflow: hidden;

            .view-btn {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 2.5rem;
              height: 2.5rem;
              background: transparent;
              border: none;
              color: var(--text-secondary);
              cursor: pointer;
              transition: all 0.2s ease;

              &:hover {
                background: var(--nav-item-hover);
                color: var(--text-primary);
              }

              &.active {
                background: var(--primary-purple);
                color: white;
              }
            }
          }

          .sort-controls {
            .sort-select {
              padding: 0.5rem 0.75rem;
              background: var(--card-bg);
              border: 1px solid var(--border-light);
              border-radius: 0.5rem;
              color: var(--text-primary);
              font-size: 0.875rem;
              cursor: pointer;
              transition: all 0.2s ease;

              &:focus {
                outline: 2px solid var(--primary-purple);
                outline-offset: 1px;
              }
            }
          }
        }
      }
      .projects-grid {
        display: grid;
        gap: 1.5rem;
        transition: all 0.3s ease;

        &[data-view="grid"] {
          grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        }

        &[data-view="list"] {
          grid-template-columns: 1fr;
          gap: 1rem;
        }

        &[data-loading="true"] {
          opacity: 0.6;
          pointer-events: none;
        }

        .project-skeleton {
          background: var(--card-bg);
          border: 1px solid var(--border-light);
          border-radius: 0.75rem;
          padding: 1rem;
          animation: pulse 1.5s ease-in-out infinite;

          .skeleton-image {
            width: 100%;
            height: 200px;
            background: var(--nav-item-hover);
            border-radius: 0.5rem;
            margin-bottom: 1rem;
          }

          .skeleton-content {
            .skeleton-title {
              height: 1.25rem;
              background: var(--nav-item-hover);
              border-radius: 0.25rem;
              margin-bottom: 0.5rem;
              width: 80%;
            }

            .skeleton-description {
              height: 1rem;
              background: var(--nav-item-hover);
              border-radius: 0.25rem;
              margin-bottom: 0.5rem;
              width: 100%;
            }

            .skeleton-meta {
              height: 1rem;
              background: var(--nav-item-hover);
              border-radius: 0.25rem;
              width: 60%;
            }
          }
        }

        .no-results {
          grid-column: 1 / -1;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 4rem 2rem;
          text-align: center;

          .no-results-icon {
            font-size: 3rem;
            color: var(--text-secondary);
            margin-bottom: 1rem;
            opacity: 0.5;
          }

          .no-results-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 0.5rem;
          }

          .no-results-description {
            font-size: 1rem;
            color: var(--text-secondary);
            margin: 0 0 1.5rem;
            max-width: 400px;
            line-height: 1.5;
          }
        }
      }
    }

    .activity-sidebar {
      background: var(--activity-bg);
      border: 1px solid var(--border-light);
      border-radius: 0.75rem;
      padding: 1.5rem;
      position: sticky;
      top: 5.5rem;
      max-height: calc(100vh - 7rem);
      overflow-y: auto;

      .sidebar-section {
        &:not(:last-child) {
          margin-bottom: 2rem;
          padding-bottom: 2rem;
          border-bottom: 1px solid var(--border-light);
        }

        h3 {
          font-size: 1rem;
          font-weight: 600;
          color: var(--text-primary);
          margin: 0 0 1rem;
        }

        .trending-list {
          display: flex;
          flex-direction: column;
          gap: 0.75rem;

          .trending-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem;
            background: transparent;
            border: 1px solid var(--border-light);
            border-radius: 0.5rem;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
              background: var(--nav-item-hover);
              border-color: var(--primary-purple);
            }

            .trending-rank {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 1.5rem;
              height: 1.5rem;
              background: var(--primary-purple);
              color: white;
              font-size: 0.75rem;
              font-weight: 600;
              border-radius: 50%;
              flex-shrink: 0;
            }

            .trending-info {
              flex: 1;
              min-width: 0;

              .trending-title {
                display: block;
                font-size: 0.875rem;
                font-weight: 500;
                color: var(--text-primary);
                margin-bottom: 0.125rem;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }

              .trending-meta {
                font-size: 0.75rem;
                color: var(--text-secondary);
              }
            }
          }
        }

        .creators-list {
          display: flex;
          flex-direction: column;
          gap: 0.75rem;

          .creator-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem;
            background: transparent;
            border: 1px solid var(--border-light);
            border-radius: 0.5rem;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
              background: var(--nav-item-hover);
              border-color: var(--primary-purple);
            }

            .creator-avatar {
              width: 2rem;
              height: 2rem;
              border-radius: 50%;
              overflow: hidden;
              flex-shrink: 0;

              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }

            .creator-info {
              flex: 1;
              min-width: 0;

              .creator-name {
                display: block;
                font-size: 0.875rem;
                font-weight: 500;
                color: var(--text-primary);
                margin-bottom: 0.125rem;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }

              .creator-projects {
                font-size: 0.75rem;
                color: var(--text-secondary);
              }
            }
          }
        }
      }
    }
  }
}

// Animations
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .showcase-page .showcase-content {
    grid-template-columns: 260px 1fr 280px;
    gap: 1rem;
    padding: 1rem;
  }
}

@media (max-width: 1024px) {
  .showcase-page .showcase-content {
    grid-template-columns: 1fr;
    gap: 1rem;

    .filters-sidebar,
    .activity-sidebar {
      position: static;
      max-height: none;
    }

    .activity-sidebar {
      order: -1;
    }
  }
}

@media (max-width: 768px) {
  .showcase-page {
    .showcase-header {
      padding: 1.5rem 0 0;

      .header-content {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
        padding: 0 1rem 1rem;

        .header-stats {
          justify-content: center;
          gap: 1.5rem;
        }
      }

      .category-tabs {
        padding: 0 1rem;
      }
    }

    .showcase-content {
      padding: 1rem;

      .projects-main .results-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;

        .results-controls {
          justify-content: space-between;
        }
      }

      .projects-main .projects-grid {
        &[data-view="grid"] {
          grid-template-columns: 1fr;
        }
      }
    }
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .showcase-page {
    .showcase-header .category-tabs .category-tab {
      border-width: 2px;

      &.active {
        font-weight: 600;
      }
    }

    .showcase-content {
      .filters-sidebar,
      .activity-sidebar {
        border-width: 2px;
      }
    }
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .showcase-page {
    * {
      transition: none !important;
      animation: none !important;
    }
  }
}
