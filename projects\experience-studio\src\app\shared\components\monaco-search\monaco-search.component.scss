.monaco-search-widget {
  position: absolute;
  top: 10px;
  right: 20px;
  z-index: 1000;
  background: var(--monaco-search-bg, #ffffff);
  border: 1px solid var(--monaco-search-border, #e0e0e0);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 320px;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.2s ease;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', 'Consolas', 'Courier New', monospace;

  &.visible {
    opacity: 1;
    transform: translateY(0);
  }
}

.search-container {
  display: flex;
  align-items: stretch;
  padding: 8px;
  gap: 4px;
}

.search-input-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background: var(--monaco-search-input-bg, #f8f9fa);
  border: 1px solid var(--monaco-search-input-border, #e0e0e0);
  border-radius: 4px;
  padding: 0 8px;
  height: 28px;
  position: relative;

  &:focus-within {
    border-color: var(--monaco-search-focus-border, #007acc);
    box-shadow: 0 0 0 1px var(--monaco-search-focus-border, #007acc);
  }
}

.search-icon {
  width: 14px;
  height: 14px;
  margin-right: 6px;
  opacity: 0.6;
}

.search-input {
  flex: 1;
  border: none;
  background: transparent;
  outline: none;
  font-size: 13px;
  font-family: inherit;
  color: var(--monaco-search-text, #333333);
  padding: 0;
  height: 100%;

  &::placeholder {
    color: var(--monaco-search-placeholder, #999999);
  }
}

.search-results-info {
  display: flex;
  align-items: center;
  margin-left: 8px;
  padding: 0 4px;
  background: var(--monaco-search-results-bg, #e8f4fd);
  border-radius: 3px;
  min-width: 60px;
  justify-content: center;
}

.results-text {
  font-size: 11px;
  color: var(--monaco-search-results-text, #0066cc);
  font-weight: 500;
  white-space: nowrap;
}

.clear-search-btn {
  background: none;
  border: none;
  padding: 2px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
  margin-left: 4px;
  width: 18px;
  height: 18px;

  &:hover {
    background: var(--monaco-search-btn-hover, #f0f0f0);
  }

  exp-icons {
    width: 12px;
    height: 12px;
  }
}

.search-options {
  display: flex;
  gap: 2px;
  margin-top: 2px;
}

.search-option-btn {
  background: none;
  border: 1px solid var(--monaco-search-option-border, #d0d0d0);
  padding: 2px 6px;
  cursor: pointer;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 600;
  color: var(--monaco-search-option-text, #666666);
  height: 20px;
  min-width: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.15s ease;

  &:hover {
    background: var(--monaco-search-option-hover, #f0f0f0);
    border-color: var(--monaco-search-option-hover-border, #b0b0b0);
  }

  &.active {
    background: var(--monaco-search-option-active, #007acc);
    border-color: var(--monaco-search-option-active-border, #007acc);
    color: var(--monaco-search-option-active-text, #ffffff);
  }
}

.option-text {
  font-family: inherit;
  font-size: inherit;
  font-weight: inherit;
}

.search-navigation {
  display: flex;
  flex-direction: column;
  gap: 1px;
  margin-left: 4px;
}

.nav-btn {
  background: none;
  border: 1px solid var(--monaco-search-nav-border, #d0d0d0);
  padding: 0;
  cursor: pointer;
  border-radius: 3px;
  width: 24px;
  height: 13px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.15s ease;

  &:hover:not(:disabled) {
    background: var(--monaco-search-nav-hover, #f0f0f0);
    border-color: var(--monaco-search-nav-hover-border, #b0b0b0);
  }

  &:disabled {
    opacity: 0.4;
    cursor: not-allowed;
  }

  exp-icons {
    width: 10px;
    height: 10px;
  }
}

.close-search-btn {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  border-radius: 3px;
  margin-left: 4px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.15s ease;

  &:hover {
    background: var(--monaco-search-close-hover, #f0f0f0);
  }

  exp-icons {
    width: 14px;
    height: 14px;
  }
}

.search-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--monaco-search-spinner-bg, #f0f0f0);
  border-top: 2px solid var(--monaco-search-spinner, #007acc);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Dark theme support
[data-theme="dark"] {
  .monaco-search-widget {
    --monaco-search-bg: #2d2d30;
    --monaco-search-border: #464647;
    --monaco-search-input-bg: #3c3c3c;
    --monaco-search-input-border: #464647;
    --monaco-search-focus-border: #007acc;
    --monaco-search-text: #cccccc;
    --monaco-search-placeholder: #999999;
    --monaco-search-results-bg: #094771;
    --monaco-search-results-text: #9cdcfe;
    --monaco-search-btn-hover: #464647;
    --monaco-search-option-border: #464647;
    --monaco-search-option-text: #cccccc;
    --monaco-search-option-hover: #464647;
    --monaco-search-option-hover-border: #6c6c6c;
    --monaco-search-option-active: #007acc;
    --monaco-search-option-active-border: #007acc;
    --monaco-search-option-active-text: #ffffff;
    --monaco-search-nav-border: #464647;
    --monaco-search-nav-hover: #464647;
    --monaco-search-nav-hover-border: #6c6c6c;
    --monaco-search-close-hover: #464647;
    --monaco-search-spinner-bg: #464647;
    --monaco-search-spinner: #007acc;
  }
}

// Global Monaco search highlight styles
:global(.monaco-search-match) {
  background-color: rgba(255, 255, 0, 0.3) !important;
  border: 1px solid rgba(255, 255, 0, 0.8) !important;
}

:global(.monaco-search-current-match) {
  background-color: rgba(255, 165, 0, 0.4) !important;
  border: 1px solid rgba(255, 165, 0, 1) !important;
}

// Dark theme search highlights
[data-theme="dark"] {
  :global(.monaco-search-match) {
    background-color: rgba(255, 255, 0, 0.2) !important;
    border: 1px solid rgba(255, 255, 0, 0.6) !important;
  }

  :global(.monaco-search-current-match) {
    background-color: rgba(255, 165, 0, 0.3) !important;
    border: 1px solid rgba(255, 165, 0, 0.8) !important;
  }
}
