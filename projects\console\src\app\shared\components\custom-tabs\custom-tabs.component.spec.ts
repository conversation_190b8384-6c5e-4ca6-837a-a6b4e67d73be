import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CustomTabsComponent } from './custom-tabs.component';
import { LucideAngularModule } from 'lucide-angular';

describe('CustomTabsComponent', () => {
  let component: CustomTabsComponent;
  let fixture: ComponentFixture<CustomTabsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CustomTabsComponent, LucideAngularModule]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(CustomTabsComponent);
    component = fixture.componentInstance;
    
    // Set up test data
    component.tabs = [
      { label: 'Prompts', value: 'prompts', icon: 'NotebookText' },
      { label: 'Models', value: 'models', icon: 'Box' },
      { label: 'Knowledge Base', value: 'knowledge', icon: 'BookOpen' },
      { label: 'Guardrails', value: 'guardrails', icon: 'Swords' },
    ];
    
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should render all tabs', () => {
    const compiled = fixture.nativeElement as HTMLElement;
    const tabItems = compiled.querySelectorAll('.tab-item');
    expect(tabItems.length).toBe(4);
  });

  it('should emit tab change event', () => {
    spyOn(component.tabChange, 'emit');
    component.onTabClick('models');
    expect(component.tabChange.emit).toHaveBeenCalledWith('models');
  });

  it('should set active tab correctly', () => {
    component.activeTab = 'models';
    expect(component.isActiveTab('models')).toBe(true);
    expect(component.isActiveTab('prompts')).toBe(false);
  });

  it('should handle disabled tabs', () => {
    component.tabs[0].disabled = true;
    expect(component.isTabDisabled('prompts')).toBe(true);
    expect(component.isTabDisabled('models')).toBe(false);
  });

  it('should return correct tab icon', () => {
    const modelTab = component.tabs[1];
    expect(component.getTabIcon(modelTab)).toBe('Box');
  });

  it('should return default icon for tab without icon', () => {
    const tabWithoutIcon = { label: 'Test', value: 'test' };
    expect(component.getTabIcon(tabWithoutIcon)).toBe('Circle');
  });
});
