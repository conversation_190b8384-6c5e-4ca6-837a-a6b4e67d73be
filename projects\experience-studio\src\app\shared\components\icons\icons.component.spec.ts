import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { IconsComponent } from './icons.component';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { By } from '@angular/platform-browser';

describe('IconsComponent', () => {
  let component: IconsComponent;
  let fixture: ComponentFixture<IconsComponent>;
  let httpMock: HttpTestingController;

  const mockSvg = `<svg fill="black" xmlns="http://www.w3.org/2000/svg"></svg>`;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule, IconsComponent]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(IconsComponent);
    component = fixture.componentInstance;
    httpMock = TestBed.inject(HttpTestingController);
    fixture.detectChanges();
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should have default iconName as awe_arrow_rightward_filled', () => {
    expect(component.iconName).toBe('awe_arrow_rightward_filled');
  });

  
});
