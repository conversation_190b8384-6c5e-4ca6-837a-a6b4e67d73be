import { Component, ChangeDetectionStrategy, inject, DestroyRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterOutlet, NavigationEnd } from '@angular/router';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { filter } from 'rxjs/operators';
import { WorkspaceStateService } from '../../services/workspace-state.service';
import { HeaderWorkspaceComponent } from '../header-workspace/header-workspace.component';

/**
 * Workspace Shell Component - Main container for the MLO Workspace
 * Provides the overall layout structure and manages workspace-wide state
 * 
 * Features:
 * - Theme management (light/dark mode)
 * - Navigation state management
 * - Responsive layout container
 * - Global workspace context
 */
@Component({
  selector: 'app-workspace-shell',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    HeaderWorkspaceComponent
  ],
  template: `
    <div 
      class="workspace-shell" 
      [attr.data-theme]="theme()"
      [attr.data-view]="currentView()">
      
      <!-- Enhanced Header -->
      <app-header-workspace></app-header-workspace>
      
      <!-- Main Workspace Content -->
      <main class="workspace-main" role="main">
        <router-outlet></router-outlet>
      </main>
      
      <!-- Global Loading Overlay -->
      @if (isGlobalLoading()) {
        <div class="global-loading-overlay" role="status" aria-label="Loading workspace">
          <div class="loading-spinner">
            <div class="spinner"></div>
            <p class="loading-text">Loading workspace...</p>
          </div>
        </div>
      }
      
      <!-- Global Error Toast -->
      @if (globalError()) {
        <div class="global-error-toast" role="alert" aria-live="polite">
          <div class="error-content">
            <i class="fas fa-exclamation-triangle" aria-hidden="true"></i>
            <span class="error-message">{{ globalError() }}</span>
            <button 
              class="error-dismiss"
              (click)="dismissGlobalError()"
              aria-label="Dismiss error">
              <i class="fas fa-times" aria-hidden="true"></i>
            </button>
          </div>
        </div>
      }
    </div>
  `,
  styleUrls: ['./workspace-shell.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class WorkspaceShellComponent {
  private readonly destroyRef = inject(DestroyRef);
  private readonly router = inject(Router);
  private readonly workspaceState = inject(WorkspaceStateService);

  // Public readonly signals from workspace state
  readonly theme = this.workspaceState.theme;
  readonly currentView = this.workspaceState.currentView;
  readonly isGlobalLoading = this.workspaceState.isGlobalLoading;
  readonly globalError = this.workspaceState.globalError;

  constructor() {
    this.setupRouteTracking();
    this.initializeWorkspace();
  }

  /**
   * Dismiss global error message
   */
  dismissGlobalError(): void {
    this.workspaceState.clearGlobalError();
  }

  /**
   * Setup route tracking to update current view state
   */
  private setupRouteTracking(): void {
    this.router.events
      .pipe(
        filter(event => event instanceof NavigationEnd),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe((event: NavigationEnd) => {
        this.updateCurrentView(event.url);
      });
  }

  /**
   * Update current view based on route
   */
  private updateCurrentView(url: string): void {
    if (url.includes('/showcase')) {
      this.workspaceState.setCurrentView('showcase');
    } else if (url.includes('/main') || url.includes('/experience')) {
      this.workspaceState.setCurrentView('dashboard');
    }
  }

  /**
   * Initialize workspace with user preferences and data
   */
  private initializeWorkspace(): void {
    // Load user theme preference
    this.workspaceState.loadUserPreferences();
    
    // Initialize workspace data
    this.workspaceState.initializeWorkspace();
  }
}
