import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { PlaygroundComponent } from './playground.component';
import {
  ButtonComponent,
  DropdownComponent,
  IconComponent,
  ToggleComponent,
  AvaTextboxComponent,
} from '@ava/play-comp-library';

describe('PlaygroundComponent', () => {
  let component: PlaygroundComponent;
  let fixture: ComponentFixture<PlaygroundComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        FormsModule,
        PlaygroundComponent,
        DropdownComponent,
        ButtonComponent,
        ToggleComponent,
        IconComponent,
        AvaTextboxComponent,
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(PlaygroundComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Agent Name Input Field', () => {
    it('should show agent name input when showAgentNameInput is true', () => {
      component.showAgentNameInput = true;
      component.displayedAgentName = 'Test Agent';
      fixture.detectChanges();

      const agentNameDisplay = fixture.debugElement.nativeElement.querySelector('.agent-name-display');
      expect(agentNameDisplay).toBeTruthy();
    });

    it('should hide agent name input when showAgentNameInput is false', () => {
      component.showAgentNameInput = false;
      fixture.detectChanges();

      const agentNameDisplay = fixture.debugElement.nativeElement.querySelector('.agent-name-display');
      expect(agentNameDisplay).toBeFalsy();
    });

    it('should display the correct agent name', () => {
      component.showAgentNameInput = true;
      component.displayedAgentName = 'My Custom Agent';
      fixture.detectChanges();

      const textboxElement = fixture.debugElement.nativeElement.querySelector('.disabled-agent-name-input ava-textbox');
      expect(textboxElement).toBeTruthy();
      expect(textboxElement.getAttribute('ng-reflect-value')).toBe('My Custom Agent');
    });

    it('should update displayed agent name when prompt selection changes', () => {
      component.showAgentNameInput = true;
      component.promptOptions = [
        { id: '1', name: 'Agent 1', value: 'agent1' },
        { id: '2', name: 'Agent 2', value: 'agent2' }
      ];
      
      // Simulate prompt change
      component.onPromptChange({ selectedValue: 'Agent 2' });
      
      expect(component.displayedAgentName).toBe('Agent 2');
    });

    it('should set initial displayed agent name from selectedValue', () => {
      component.showAgentNameInput = true;
      component.selectedValue = 'Initial Agent';
      component.displayedAgentName = '';
      
      component.ngOnInit();
      
      expect(component.displayedAgentName).toBe('Initial Agent');
    });

    it('should not override existing displayedAgentName if already set', () => {
      component.showAgentNameInput = true;
      component.selectedValue = 'Initial Agent';
      component.displayedAgentName = 'Existing Agent';
      
      component.ngOnInit();
      
      expect(component.displayedAgentName).toBe('Existing Agent');
    });
  });

  describe('Dropdown and Agent Name Input Combination', () => {
    it('should show both dropdown and agent name input when both are enabled', () => {
      component.showDropdown = true;
      component.showAgentNameInput = true;
      fixture.detectChanges();

      const dropdown = fixture.debugElement.nativeElement.querySelector('.dropdown-container');
      const agentNameDisplay = fixture.debugElement.nativeElement.querySelector('.agent-name-display');
      
      expect(dropdown).toBeTruthy();
      expect(agentNameDisplay).toBeTruthy();
    });

    it('should show only agent name input when dropdown is disabled', () => {
      component.showDropdown = false;
      component.showAgentNameInput = true;
      fixture.detectChanges();

      const dropdown = fixture.debugElement.nativeElement.querySelector('.dropdown-container');
      const agentNameDisplay = fixture.debugElement.nativeElement.querySelector('.agent-name-display');
      
      expect(dropdown).toBeFalsy();
      expect(agentNameDisplay).toBeTruthy();
    });
  });

  describe('Input Properties', () => {
    it('should use custom placeholder when provided', () => {
      component.showAgentNameInput = true;
      component.agentNamePlaceholder = 'Custom Placeholder';
      fixture.detectChanges();

      const textboxElement = fixture.debugElement.nativeElement.querySelector('.disabled-agent-name-input ava-textbox');
      expect(textboxElement.getAttribute('ng-reflect-placeholder')).toBe('Custom Placeholder');
    });

    it('should use default placeholder when not provided', () => {
      component.showAgentNameInput = true;
      fixture.detectChanges();

      const textboxElement = fixture.debugElement.nativeElement.querySelector('.disabled-agent-name-input ava-textbox');
      expect(textboxElement.getAttribute('ng-reflect-placeholder')).toBe('Agent Name');
    });

    it('should have disabled and readonly attributes set', () => {
      component.showAgentNameInput = true;
      fixture.detectChanges();

      const textboxElement = fixture.debugElement.nativeElement.querySelector('.disabled-agent-name-input ava-textbox');
      expect(textboxElement.getAttribute('ng-reflect-disabled')).toBe('true');
      expect(textboxElement.getAttribute('ng-reflect-readonly')).toBe('true');
    });
  });
});
