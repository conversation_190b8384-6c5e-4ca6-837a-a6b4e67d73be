:host {
  display: inline-block;

  &.theme-dark {
    --awe-pill-background: rgba(255, 255, 255, 0.1);
    --awe-pill-border: 1px solid rgba(255, 255, 255, 0.2);
    --awe-pill-hover-border: 1px solid rgba(255, 255, 255, 0.3);
    --awe-pill-text-color: #ffffff;
    --awe-dropdown-background: #2d2d2d;
    --awe-dropdown-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    --awe-dropdown-text-color: #ffffff;
    --awe-dropdown-divider-color: rgba(255, 255, 255, 0.1);
    --awe-dropdown-item-hover: rgba(255, 255, 255, 0.1);
  }

  &.theme-light {
    --awe-pill-background: transparent;
    --awe-pill-border: none;
    --awe-pill-hover-border: 0.5px solid rgba(0, 0, 0, 0.1);
    --awe-pill-text-color: #333333;
    --awe-dropdown-background: #ffffff;
    --awe-dropdown-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    --awe-dropdown-text-color: #333333;
    --awe-dropdown-divider-color: rgba(0, 0, 0, 0.1);
    --awe-dropdown-item-hover: #f5f5f5;
  }
}

.file-attach-pill-container {
  position: relative;
  display: inline-block;
}

.file-attach-pill {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
  padding: 8px;
  background-color: var(--awe-pill-background, transparent);
  border: var(--awe-pill-border, none);
  border-radius: 8px;
  cursor: pointer;
  overflow: hidden;
  width: 36px;
  height: 36px;
  transition: width 0.3s ease, box-shadow 0.2s ease;
  white-space: nowrap;

  &:hover,
  &:focus {
    border: var(--awe-pill-hover-border, 0.5px solid rgba(0, 0, 0, 0.1));
  }

  &.expanded {
    width: 160px;
    justify-content: flex-start;
  }

  .icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    flex-shrink: 0;

    exp-icons {
      // width: 24px;
      height: 24px;
      transform: scale(1.2);
    }
  }

  .text {
    font-size: 12px;
    color: var(--awe-pill-text-color, #333);
    margin: 0 4px;
    flex-grow: 1;
  }

  .arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    flex-shrink: 0;
    opacity: 0;
    transform: translateX(-10px);
    transition: opacity 0.3s ease, transform 0.3s ease;
    margin-left: auto;
  }

  &.expanded .arrow {
    opacity: 1;
    transform: translateX(0);
  }
}

.dropdown {
  position: absolute;
  bottom: calc(100% + 8px);
  left: 0;
  min-width: 180px;
  background: var(--awe-dropdown-background, white);
  border-radius: 8px;
  box-shadow: var(--awe-dropdown-shadow, 0 -4px 12px rgba(0, 0, 0, 0.15));
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: opacity 0.3s ease, visibility 0.3s ease, transform 0.3s ease;
  z-index: 100;
  overflow: hidden;

  &.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
}

.dropdown-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  padding: 10px 12px;
  cursor: pointer;
  transition: background 0.3s ease;
  position: relative;
  margin: 4px 0; /* Add vertical spacing between items */

  &:not(:last-child)::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 12px;
    right: 12px;
    height: 1px;
    background-color: var(--awe-dropdown-divider-color, rgba(0, 0, 0, 0.1));
  }

  &:hover,
  &:focus {
    background-color: var(--awe-dropdown-item-hover, #f5f5f5);
    outline: none;
  }

  .dropdown-item-text {
    font-size: 14px;
    color: var(--awe-dropdown-text-color, #333);
    flex-grow: 1;
  }

  .dropdown-item-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    flex-shrink: 0;
  }

  exp-icons {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    flex-shrink: 0;
  }
}
