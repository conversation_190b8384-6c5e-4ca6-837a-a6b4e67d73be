import { HttpHeaders, HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class CollaborativeAgentServiceService {
  private baseUrl = environment.consoleApi;
  private baseUrl2 = environment.consoleApiV2;
  private headers = {
    headers: new HttpHeaders({
      'Content-Type': 'application/json',
    }),
  };

  constructor(private http: HttpClient) {}

  public getGenerativeModel() {
    const url = `${this.baseUrl}/ava/force/model?modelType=Generative`;
    return this.http.get(url, this.headers).pipe(
      map((Response: any) => {
        return Response;
      }),
    );
  }

  public getKnowledgeBases() {
    const embeddingUrl = environment.consoleEmbeddingApi;
    const url = `${embeddingUrl}/ava/force/knowledge`;
    return this.http.get(url, this.headers).pipe(
      map((Response: any) => {
        return Response;
      }),
    );
  }

  public getAllCollaborativeAgents(type = 'summary'): Observable<any[]> {
    const url = `${this.baseUrl}/ava/force/collaborativeAgent`;
    return this.http.get(url, { ...this.headers, params: { type } }).pipe(
      map((Response: any) => {
        return Response?.agentDetails || [];
      }),
    );
  }

  public getAllCollaborativeAgentsPagination(page: number, records: number, isDeleted: boolean): Observable<any> {
    const url = `${this.baseUrl2}/ava/force/da/agent/approved`;
    const params = new HttpParams()
      .set('page', page.toString())
      .set('records', records.toString())
      .set('isDeleted', isDeleted.toString());

    return this.http.get<any>(url, { params });
  }
}
