# =============================================================================
# Local Development Environment Configuration
# =============================================================================
# 
# This file contains environment variables for local development.
# Use this for running the application stack locally on your development machine.
# 
# Usage: docker-compose --env-file env.local up -d
# =============================================================================

# =====================================================================
# Environment Information
# =====================================================================
ENVIRONMENT=local
BASE_URL=http://localhost

# =====================================================================
# Docker Configuration
# =====================================================================
COMPOSE_PROJECT_NAME=elderwand-local

# =====================================================================
# Application Ports (for local development)
# =====================================================================
# Nginx reverse proxy port
NGINX_PORT=80

# Internal application ports (exposed for debugging if needed)
CONSOLE_PORT=4001
EXPERIENCE_PORT=4002
LAUNCHPAD_PORT=4003
PRODUCT_PORT=4004

# =====================================================================
# Build Configuration
# =====================================================================
# Angular build configuration
ANGULAR_CONFIGURATION=development

# Build cache settings for faster local builds
DOCKER_BUILDKIT=1
COMPOSE_DOCKER_CLI_BUILD=1

# =====================================================================
# Development Features
# =====================================================================
# Enable development features
DEBUG_MODE=true
ENABLE_SOURCE_MAPS=true
ENABLE_LIVE_RELOAD=false

# =====================================================================
# Logging Configuration
# =====================================================================
LOG_LEVEL=debug
NGINX_LOG_LEVEL=info

# =====================================================================
# Health Check Configuration
# =====================================================================
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=10s
HEALTH_CHECK_RETRIES=3

# =====================================================================
# Local Development Overrides
# =====================================================================
# Use local build context (not pipeline artifacts)
USE_LOCAL_BUILD=true

# Skip external dependencies for local development
SKIP_EXTERNAL_DEPS=true

# =====================================================================
# Security (relaxed for local development)
# =====================================================================
# Disable HTTPS redirects for local development
FORCE_HTTPS=false

# Allow all origins for CORS in local development
CORS_ALLOW_ALL=true

# =====================================================================
# Resource Limits (optimized for local development)
# =====================================================================
# Memory limits for containers
NGINX_MEMORY_LIMIT=128m
APP_MEMORY_LIMIT=512m

# CPU limits
APP_CPU_LIMIT=0.5

# =====================================================================
# Cache Configuration
# =====================================================================
# Disable aggressive caching for local development
ENABLE_BROWSER_CACHE=false
STATIC_CACHE_TTL=60s

# =====================================================================
# Development Tools
# =====================================================================
# Enable development tools and debugging
ENABLE_DEV_TOOLS=true
ENABLE_PERFORMANCE_MONITORING=false 