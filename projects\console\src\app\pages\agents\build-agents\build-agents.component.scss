@mixin hide-scrollbar {
  &::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
  }
  scrollbar-width: none;
  -ms-overflow-style: none;
}

// Global tooltip scroll fix - ensure all tooltips can scroll properly
::ng-deep {
  // Fix for ava-tooltip component scrolling issues
  ava-tooltip,
  .ava-tooltip,
  [class*="tooltip"] {
    .tooltip-content,
    .tooltip-body,
    .tooltip-text {
      // Remove any text truncation that might be applied globally
      text-overflow: clip !important;
      -webkit-line-clamp: unset !important;
      line-clamp: unset !important;
      -webkit-box-orient: unset !important;
      display: block !important;

      // Enable scrolling for long content
      max-height: 300px !important;
      overflow-y: auto !important;
      overflow-x: hidden !important;
      white-space: pre-line !important;
      word-wrap: break-word !important;

      // Ensure scrollbar is visible
      scrollbar-width: thin;
      scrollbar-color: rgba(0, 0, 0, 0.3) rgba(0, 0, 0, 0.1);

      &::-webkit-scrollbar {
        width: 6px !important;
        height: 6px !important;
      }

      &::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.1) !important;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.3) !important;
        border-radius: 3px;

        &:hover {
          background: rgba(0, 0, 0, 0.5) !important;
        }
      }
    }
  }

  // Specific fix for any tooltip that might have fixed height restrictions
  .tooltip-wrapper,
  .tooltip-container {
    max-height: none !important;
    height: auto !important;
  }
}

.build-agents-container {
  height: 89vh;
  display: flex;
  flex-direction: column;
  background-color: var(--background-primary);
  overflow: hidden;

  .header-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 60px;
    background-color: var(--card-bg);
    border-bottom: 1px solid var(--border-color);

    .breadcrumb {
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 1;

      .nav-item {
        font-size: 14px;
        color: var(--text-secondary);
        cursor: pointer;
        transition: color 0.2s ease;

        &:hover {
          color: var(--text-primary);
        }

        &.active {
          color: var(--text-primary);
          font-weight: 500;
        }
      }

      .separator {
        color: var(--text-tertiary);
        font-size: 14px;
      }

      .close-btn {
        background: none;
        border: none;
        color: var(--text-secondary);
        cursor: pointer;
        padding: 4px;
        // border-radius: 4px;
        margin-left: 16px;
        transition: all 0.2s ease;

        &:hover {
          background-color: var(--hover-bg);
          color: var(--text-primary);
        }
      }
    }

    .header-actions {
      .action-group {
        display: flex;
        align-items: center;
        gap: 8px;

        .action-btn {
          background: none;
          border: 1px solid var(--border-color);
          color: var(--text-secondary);
          cursor: pointer;
          padding: 8px;
          // border-radius: 6px;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            background-color: var(--hover-bg);
            border-color: var(--border-hover);
            color: var(--text-primary);
          }
        }

        .run-btn {
          background: var(--dashboard-primary);
          border: 1px solid var(--dashboard-primary);
          color: white;
          cursor: pointer;
          padding: 8px 16px;
          // border-radius: 6px;
          font-size: 14px;
          font-weight: 500;
          display: flex;
          align-items: center;
          gap: 6px;
          transition: all 0.2s ease;

          &:hover {
            background: var(--dashboard-primary-hover);
            border-color: var(--dashboard-primary-hover);
          }
        }
      }
    }
  }

  .main-content {
    display: flex;
    flex-direction: column; // Changed from row to column
    flex: 1;
    overflow: hidden;
    height: calc(100vh - 60px); // Full height minus header
    max-height: calc(100vh - 60px);
    position: relative;

    .canvas-area {
      flex: 1;
      background-color: var(--background-primary);
      overflow: hidden;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative; // Added for absolute positioning of panel

      // Configure Agent Floating Panel - Positioned on canvas area
      .configure-agent-panel {
        position: absolute;
        top: 20px;
        left: 20px;
        width: 380px; // Increased width to match design
        height: 80vh;
        margin-top: 2rem;
        margin-bottom: 2rem;
        background-color: #ffffff; // Pure white background
        border: 1px solid var(--Brand-Neutral-n-50, #f0f1f2);
        border-radius: 8px; // More rounded corners
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08); // Softer shadow
        z-index: 10; // Lower z-index so agent name section can appear beside it
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        overflow: hidden;
        display: flex;
        flex-direction: column; // Use flexbox for proper height distribution

        // Hide panel in execute mode
        .main-content.execute-mode & {
          display: none;
        }

        // Collapsed state
        &.collapsed {
          width: auto;
          min-width: 200px;
          height: auto;

          .panel-header {
            padding: 12px 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-color: #ffffff;
            border-bottom: none;

            h3 {
              margin: 0;
              font-size: 14px;
              font-weight: 600;
              color: var(--text-primary);
            }

            ava-icon {
              transition: transform 0.3s ease;
            }
          }

          .panel-content {
            display: none;
          }
        }

        .panel-header {
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: space-between;
          background-color: #ffffff;
          transition: all 0.3s ease;

          &:hover {
            background-color: #f9fafb;
          }

          h3 {
            margin: 0;
            font-size: 20px;
            font-weight: 700;
            color: #4c515b;
          }

          ava-icon {
            transition: transform 0.3s ease;
          }
        }

        .panel-content {
          margin-top: 1rem;
          flex: 1; // Take remaining height
          overflow: hidden;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          display: flex;
          flex-direction: column;

          &.hidden {
            max-height: 0;
            opacity: 0;
          }

          .tools-section {
            display: flex;
            flex-direction: column;
            height: 100%; // Full height
            overflow: hidden;

            .custom-tabs-container {
              flex-shrink: 0; // Don't shrink tabs

              .builder-custom-tabs {
                @include hide-scrollbar;
              }

              // Custom Tabs Container Styles
              .custom-tabs-wrapper {
                display: flex;
                width: 100%;
                gap: 8px; // Consistent gap
                justify-content: space-between;
                padding: 0;
                min-width: 0; // Allow shrinking

                .tab-item {
                  flex: 1;
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  padding: 12px 8px; // Adjusted padding
                  border: none;
                  background-color: transparent;
                  cursor: pointer;
                  min-height: 56px; // Fixed height
                  min-width: 0; // Allow shrinking
                  transition: all 0.2s ease; // Smooth transitions
                  // border-radius: 8px; // Rounded corners

                  // No hover effects for cleaner design

                  &.active {
                    .tab-icon-box {
                      background-color: #3b82f6; // Blue background for active
                      border-color: #3b82f6;

                      lucide-icon {
                        color: #ffffff !important; // White icon
                      }
                    }

                    // Keep label black even when active
                    .tab-label {
                      color: #111827;
                      font-weight: 600;
                    }
                  }

                  &.disabled {
                    opacity: 0.5;
                    cursor: not-allowed;
                  }

                  .tab-icon-box {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 32px; // Smaller size
                    height: 32px; // Smaller size
                    border: 1px solid #d1d5db;
                    // border-radius: 8px; // More rounded
                    background-color: #f9fafb; // Light grey background
                    margin-bottom: 6px; // More spacing

                    .tab-png-icon {
                      width: 16px; // Smaller icon
                      height: 16px; // Smaller icon
                      object-fit: contain;
                    }

                    lucide-icon {
                      width: 16px; // Smaller icon
                      height: 16px; // Smaller icon
                      color: #6b7280; // Grey color
                    }
                  }

                  .tab-label {
                    font-size: 12px; // Smaller font
                    font-weight: 500;
                    color: #6b7280; // Grey color
                    text-align: center;
                    line-height: 1.2;
                  }
                }
              }
            }

            .search-section {
              margin-top: 1rem;
              flex-shrink: 0; // Don't shrink search

              form {
                width: 100%;
              }

              // Search input styling
              ::ng-deep ava-textbox {
                .textbox-container {
                  // border-radius: 12px; // More rounded
                  border: 1px solid #e5e7eb;
                  background-color: #f9fafb;

                  &:focus-within {
                    border-color: #3b82f6;
                    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                  }

                  input {
                    background-color: transparent;
                    color: #111827;
                    font-size: 14px;

                    &::placeholder {
                      color: #9ca3af;
                    }
                  }
                }
              }
            }

            .tools-list {
              flex: 1; // Take remaining space
              overflow-y: auto; // Make scrollable
              overflow-x: hidden;
              @include hide-scrollbar;
              min-height: 0; // Important for flex child to scroll

              .tool-item {
                border: 1px solid var(--Brand-Neutral-n-50, #f0f1f2);
                background-color: #ffffff;
                border-radius: 8px;
                padding: 16px;
                margin-top: 16px;
                cursor: grab;
                transition: all 0.2s ease;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
                box-shadow: 0px 0px 3px 0px #00000014;

                &:hover {
                  border-color: var(--Global-colors-Royal-blue-200, #bbcff9);
                  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                  transform: translateY(-1px);
                }

                &:active {
                  cursor: grabbing;
                }

                .tool-header {
                  display: flex;
                  align-items: center;
                  gap: 12px; // More spacing
                  margin-bottom: 8px; // More spacing

                  .tool-icon-box {
                    width: 36px;
                    height: 36px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-shrink: 0;
                    background: var(--Global-colors-Royal-blue-50, #e9effd);
                    border-radius: 999px;

                    ava-icon {
                      color: #ffffff; // White icon
                    }
                  }

                  .tool-name {
                    margin: 0;
                    font-size: 16px;
                    font-weight: 600;
                    color: var(--Brand-Neutral-n-800, #3b3f46) !important;
                    flex: 1;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                  }

                  .tool-count {
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    font-weight: 600;
                    font-size: 16px;
                    color: var(--Colors-Text-primary, #4c515b);

                    .count-text {
                      font-weight: 500;
                    }
                  }
                }

                .tool-description {
                  margin: 0 0 16px 0; // More spacing
                  font-size: 14px; // Larger font
                  color: var(--Colors-Text-secondary, #616874);
                  line-height: 1.5; // Better line height
                  display: -webkit-box;
                  -webkit-line-clamp: 2;
                  -webkit-box-orient: vertical;
                  overflow: hidden;
                }

                .tool-actions {
                  display: flex;
                  justify-content: flex-end;

                  .preview-btn {
                    font-size: 16px;
                    // &:hover {
                    //   background-color: #e5e7eb;
                    // }
                  }
                }
              }

              .no-results-message {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 2rem 1rem;
                text-align: center;

                .no-results-content {
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  gap: 0.5rem;

                  p {
                    margin: 0;
                    color: #6b7280; // Grey color
                    font-size: 14px;
                  }
                }
              }
            }

            .create-tool-section {
              margin-top: 16px;
              border-top: 1px solid #f3f4f6; // Lighter border
              background-color: #ffffff;
              flex-shrink: 0; // Don't shrink create section

              // Create button styling
              ::ng-deep ava-button {
                .button-container {
                  // border-radius: 12px; // More rounded
                  background: linear-gradient(
                    135deg,
                    #3b82f6 0%,
                    #1d4ed8 100%
                  ); // Gradient
                  border: none;
                  font-weight: 600;
                  font-size: 14px;
                  padding: 12px 20px; // More padding

                  &:hover {
                    background: linear-gradient(
                      135deg,
                      #2563eb 0%,
                      #1e40af 100%
                    );
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
                  }
                }
              }
            }
          }
        }
      }

      .editor-canvas {
        height: 100%;
        display: flex;
        flex-direction: column;

        h2 {
          margin: 0;
          padding: 1rem;
          font-size: 18px;
          font-weight: 600;
          color: var(--text-primary);
          border-bottom: 1px solid var(--border-color);
        }

        app-canvas-board {
          flex: 1;
          overflow: hidden;
        }

        // Canvas board specific styles
        ::ng-deep app-canvas-board {
          height: 100%;
          width: 100%;

          .canvas-container {
            height: 100%;
            background-color: var(--background-secondary);
          }

          .canvas-viewport {
            height: 100%;
          }

          .fallback-message {
            color: var(--text-tertiary);
            font-size: 16px;
            text-align: center;
            padding: 40px;
          }

          .floating-toolbar {
            .primary-btn {
              background: linear-gradient(
                45deg,
                lightblue,
                darkblue
              ) !important;
            }
          }

          // Enhanced connection lines styling
          .canvas-edges {
            .edge-path {
              stroke: #9ca1aa; // Match marker color for consistency
              stroke-width: 1.5px; // Thinner lines as requested
              fill: none;
              transition: none; // Remove transition for smooth movement

              &:hover {
                stroke: #9ca1aa; // Keep same color on hover
                stroke-width: 2px; // Slightly thicker on hover
                transition: stroke-width 0.1s ease; // Only transition hover effects
              }
            }

            .edge-marker {
              fill: #9ca1aa; // Match connection line color
              stroke: none;
            }

            .arrow-head {
              fill: #9ca1aa; // Match connection line color exactly
            }

            // Animated flow effect
            .edge-animated {
              stroke-dasharray: 5;
              animation: dash 1s linear infinite;
            }
          }

          // Enhanced node styling
          .canvas-nodes {
            .node {
              filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.1));
              transition: all 0.3s ease;

              &:hover {
                filter: drop-shadow(0 6px 20px rgba(0, 0, 0, 0.15));
                transform: translateY(-2px);
              }

              .node-content {
                // border-radius: 16px;
                border: 2px solid #e5e7eb;
                background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
                transition: all 0.3s ease;

                &:hover {
                  border-color: #4f46e5;
                  background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
                }
              }

              &.selected .node-content {
                border-color: #4f46e5;
                background: linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%);
                box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
              }
            }
          }

          // Agent details dropdown styling (only for build mode, not execute mode)
          &:not(.execute-mode) .header-inputs-section {
            position: absolute;
            top: 52px !important;
            left: 420px !important;
            z-index: 20;
            display: flex;
            flex-direction: column;
            gap: 12px;
            max-width: calc(100% - 440px);

            .agent-details-dropdown {
              min-width: 300px !important;
              max-width: 400px; // Limit maximum width
              transition: min-width 0.3s ease;

              ::ng-deep .ava-dropdown {
                min-width: 300px !important;
                max-width: 400px; // Limit maximum width

                .dropdown-trigger {
                  min-width: 300px !important;
                  max-width: 400px; // Limit maximum width
                  padding: 12px 16px;
                  // border-radius: 12px;
                  border: 2px solid #e5e7eb;
                  background: #ffffff;
                  transition: all 0.3s ease;
                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

                  &:hover {
                    border-color: #4f46e5;
                    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
                  }

                  &:focus {
                    border-color: #4f46e5;
                    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
                  }
                }

                .dropdown-content {
                  min-width: 300px !important;
                  max-width: 400px; // Limit maximum width
                  // border-radius: 12px;
                  border: 2px solid #e5e7eb;
                  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
                  backdrop-filter: blur(8px);
                }
              }
            }

            .agent-name-input {
              max-width: 400px; // Limit maximum width

              ::ng-deep .ava-textbox {
                max-width: 400px; // Limit maximum width

                input {
                  // border-radius: 12px;
                  border: 2px solid #e5e7eb;
                  transition: all 0.3s ease;
                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

                  &:hover {
                    border-color: #4f46e5;
                    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
                  }

                  &:focus {
                    border-color: #4f46e5;
                    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
                  }
                }
              }
            }
          }

          // Canvas container adjustments to prevent overlap with Configure Agent panel
          .canvas-container {
            margin-left: 420px; // Add left margin to prevent overlap with Configure Agent panel
            width: calc(100% - 420px); // Adjust width to account for the margin
            height: 100%;
            position: relative;
          }

          // Canvas tools toolbar positioning to avoid overlap
          .canvas-tools-toolbar {
            position: absolute;
            bottom: 50px;
            right: 20px;
            width: fit-content;
            z-index: 15; // Lower than Configure Agent panel but higher than canvas content
          }

          // Floating toolbar positioning
          .floating-toolbar {
            position: absolute;
            top: 52px;
            right: 20px;
            z-index: 15;
          }
        }

        // Execute mode specific styles
        &.execute-mode {
          ::ng-deep app-canvas-board {
            .canvas-container {
              display: flex;
              justify-content: center;
              align-items: flex-start;
              width: 100%;
              height: 100%;
            }

            .canvas-viewport {
              transform-origin: center top;
              width: 100%;
              display: flex;
              justify-content: center;
            }

            .nodes-container {
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: flex-start;
              width: 100%;
              position: relative;
            }

            // Ensure connections are properly centered
            .connections-layer {
              width: 100%;
              height: 100%;
              position: absolute;
              top: 0;
              left: 0;
              display: flex;
              justify-content: center;
            }

            .floating-toolbar {
              // Keep execute button visible in execute mode
              position: absolute;
              top: 20px;
              right: 20px;
              z-index: 1000;
            }

            .header-inputs-section {
              display: block;
              position: absolute;
              top: 20px;
              left: 20px;
              z-index: 1000;

              .agent-name-input {
                width: 180px !important;

                ::ng-deep .ava-textbox {
                  width: 180px !important;
                }
              }

              // In execute mode, set dropdown to 76% width
              .agent-details-dropdown {
                width: 76% !important;
                min-width: 200px !important;

                ::ng-deep .ava-dropdown {
                  width: 100% !important;
                  min-width: 200px !important;

                  .dropdown-trigger {
                    width: 100% !important;
                    min-width: 200px !important;
                    padding: 8px 12px;
                  }

                  .dropdown-content {
                    width: 100% !important;
                    min-width: 200px !important;
                  }
                }
              }
            }
          }
        }
      }
    }

    // Execute mode - when in execute mode, show playground area
    &.execute-mode {
      flex-direction: row; // Change to row layout for side-by-side

      .canvas-area {
        flex: 0 0 50%; // Canvas takes 50% width in execute mode
        min-width: 400px; // Ensure minimum width for visibility
        height: 100%; // Full height
        display: flex;
        flex-direction: column; // Stack canvas board properly
        justify-content: flex-start;
        align-items: stretch;
        // border-right: 1px solid #e5e7eb; // Removed border between canvas and playground
        overflow: hidden; // Prevent overflow
        position: relative; // For proper positioning

        // Ensure canvas board is visible in execute mode
        .editor-canvas {
          flex: 1; // Take remaining space
          height: 100%; // Full height
          width: 100%; // Full width
          position: relative;
          overflow: hidden;

          ::ng-deep app-canvas-board {
            width: 100%;
            height: 100%;
            display: block; // Ensure it's displayed
          }
        }
      }

      .playground-area {
        flex: 0 0 50%; // Playground takes 50% width in execute mode
        position: relative;
        background-color: var(--card-bg);
        height: 100%; // Full height
        overflow: hidden;
        display: flex;
        flex-direction: column;
        padding: 10px;
        // border: 1px solid #dcdcdc;
        // border-radius: 16px;
        margin: 10px;
        margin-left: 0; // Remove left margin since we have border

        .exit-execute-btn {
          background: none;
          border: none;
          cursor: pointer;
          padding: 4px;
          // border-radius: 4px;
          color: var(--text-secondary);
          transition: all 0.2s ease;

          &:hover {
            background-color: var(--background-secondary);
            color: var(--text-primary);
          }

          svg {
            width: 16px;
            height: 16px;
          }
        }

        ::ng-deep app-playground {
          flex: 1;
          height: calc(100% - 48px);
          width: 100%;

          .playground-container {
            height: 100%;
            // border-radius: 0;
          }
        }
      }
    }
  }
}

// Preview Panel Styles
.preview-panel {
  position: fixed;
  top: 0;
  right: -400px;
  width: 400px;
  height: 100vh;
  background-color: var(--card-bg);
  border-left: 1px solid var(--border-color);
  box-shadow: -4px 0 16px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;

  &.visible {
    right: 0;
  }

  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--background-primary);

    .preview-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--text-primary);
    }

    .close-preview-btn {
      background: none;
      border: none;
      color: var(--text-secondary);
      cursor: pointer;
      padding: 4px;
      // border-radius: 4px;
      transition: all 0.2s ease;

      &:hover {
        background-color: var(--hover-bg);
        color: var(--text-primary);
      }
    }
  }

  .preview-content {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    @include hide-scrollbar;

    .preview-loading {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 2rem;
      text-align: center;

      .loading-spinner {
        width: 32px;
        height: 32px;
        border: 3px solid var(--border-color);
        border-top: 3px solid var(--dashboard-primary);
        // border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 1rem;
      }

      p {
        margin: 0;
        color: var(--text-secondary);
        font-size: 14px;
      }
    }

    .preview-error {
      padding: 1rem;
      text-align: center;

      p {
        margin: 0;
        color: #ef4444;
        font-size: 14px;
      }
    }

    .preview-field {
      margin-bottom: 1rem;

      label {
        display: block;
        font-size: 12px;
        font-weight: 600;
        color: var(--text-secondary);
        margin-bottom: 0.25rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      span {
        display: block;
        font-size: 14px;
        color: var(--text-primary);
        line-height: 1.4;
      }

      .content-preview {
        background-color: var(--background-secondary);
        border: 1px solid var(--border-color);
        // border-radius: 4px;
        padding: 0.75rem;
        font-size: 12px;
        color: var(--text-primary);
        white-space: pre-wrap;
        word-break: break-word;
        max-height: 200px;
        overflow-y: auto;
        margin: 0;
      }
    }
  }
}

// Animation for loading spinner
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Animation for edge flow
@keyframes dash {
  to {
    stroke-dashoffset: -10;
  }
}

// Additional smooth transitions for nodes
@keyframes node-pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

@keyframes connection-glow {
  0%,
  100% {
    filter: drop-shadow(0 2px 4px rgba(79, 70, 229, 0.2));
  }
  50% {
    filter: drop-shadow(0 4px 8px rgba(79, 70, 229, 0.4));
  }
}

/* Agent Playground Controls */
.agent-playground-controls {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  // border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;

  .agent-info {
    margin-bottom: 12px;

    h4 {
      margin: 0;
      color: #495057;
      font-size: 16px;
      font-weight: 600;
    }
  }

  .agent-toggles {
    display: flex;
    gap: 16px;
    align-items: center;
    margin-bottom: 12px;
    flex-wrap: wrap;

    .toggle-row {
      display: flex;
      align-items: center;
      min-height: 32px;
    }

    .clear-chat-btn {
      background: #dc3545;
      color: white;
      border: none;
      padding: 6px 12px;
      // border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
      transition: background-color 0.2s ease;

      &:hover {
        background: #c82333;
      }
    }
  }

  .agent-file-upload {
    .file-upload-btn {
      background: #007bff;
      color: white;
      border: none;
      padding: 8px 16px;
      // border-radius: 4px;
      font-size: 14px;
      cursor: pointer;
      transition: background-color 0.2s ease;

      &:hover {
        background: #0056b3;
      }
    }

    .agent-uploaded-files {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-top: 8px;

      .file-item {
        display: flex;
        align-items: center;
        background: #e9ecef;
        border: 1px solid #ced4da;
        // border-radius: 4px;
        padding: 4px 8px;
        font-size: 12px;
        max-width: 200px;

        .file-name {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-right: 8px;
        }

        .remove-file {
          background: none;
          border: none;
          color: #6c757d;
          cursor: pointer;
          font-size: 16px;
          line-height: 1;
          padding: 0;
          width: 16px;
          height: 16px;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            color: #dc3545;
          }
        }
      }
    }
  }
}

// API Response Modal Styling
.modal-content-wrapper {
  padding: 16px 0;

  p {
    margin: 0;
    line-height: 1.5;
    font-size: 14px;
    color: var(--text-primary);
    white-space: pre-wrap; // Preserve line breaks in JSON responses
  }

  &.error-content {
    .error-text {
      color: #dc3545; // Red color for errors
      font-weight: 500;
    }
  }
}

::ng-deep .tools-section .ava-button.secondary {
  --button-effect-color: var(--button-variant-secondary-effect-color);
  border-radius: 999px !important;
  background-color: var(--Global-colors-Royal-blue-50, #e9effd) !important;
  border: none !important;
}

::ng-deep .tool-icon-box svg {
  stroke: var(--Global-colors-Royal-blue-700, #1a46a7);
}

// Prompt Configuration Popup Styles - Libraries Layout
::ng-deep ava-popup[ng-reflect-popup-width="90%"] {
  .popup-content {
    max-width: 1100px !important;
    width: 90% !important;
    height: 90vh !important;
    max-height: 90vh !important;
  }
}

.prompt-configuration-container {
  display: flex;
  flex-direction: column;
  height: calc(90vh - 120px);
  padding: 24px 20px;
  overflow: hidden;
  width:1300px;

  // Single Column Layout - Figma Design
  .main-column {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    max-width: 100%;
    width: 100%;

    .tab-container {
      margin-bottom: 32px;
      text-align: center;

      // Override center alignment for form content
      .template-form {
        text-align: left !important;
      }

      .tab-heading {
        margin: 0 0 24px 0;
        font-size: 18px;
        font-weight: 600;
        color: #111827;
      }

      .tabs-wrapper {
        .pill-tabs-container {
          display: flex;
          justify-content: center;
          gap: 0;

          ::ng-deep ava-button {
            .ava-button {
              margin: 0 !important;
              min-width: 120px !important;
              padding: 12px 24px !important;
              font-size: 14px !important;
              font-weight: 500 !important;

              &.ava-button--primary {
                background: #4285f4 !important;
                color: #ffffff !important;
                border: 1px solid #4285f4 !important;
                z-index: 2;
                position: relative;
              }

              &.ava-button--secondary {
                background: #f8f9fa !important;
                color: #5f6368 !important;
                border: 1px solid #d1d5db !important;

                &:hover {
                  background: #e8eaed !important;
                  color: #5f6368 !important;
                }
              }
            }

            &:first-child .ava-button {
              border-radius: 20px 0 0 20px !important;
              border-right: none !important;
            }

            &:last-child .ava-button {
              border-radius: 0 20px 20px 0 !important;
            }
          }
        }
      }
    }

    .form-container {
      flex: 1;
      overflow-y: auto;
      margin-bottom: 24px;
      width: 100%;

      /* Hide scrollbar but keep functionality */
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE and Edge */

      &::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
      }

      .tab-content {
        height: 100%;
        width: 100%;
      }
    }

    // Regenerate Button Wrapper
    .regenerate-button-wrapper {
      display: flex !important;
      justify-content: flex-end !important;
      margin: 20px 0 !important;
      padding-right: 0 !important;
      width: 100% !important;
      visibility: visible !important;

      ::ng-deep ava-button {
        // Regenerate Button - Figma Style
        .ava-button {
          background: transparent !important;
          border: 1px solid #4285f4 !important;
          color: #4285f4 !important;
          min-width: auto !important;
          padding: 8px 16px !important;
          font-size: 14px !important;
          font-weight: 500 !important;
          border-radius: 6px !important;
          height: auto !important;

          &:hover {
            background: #f8faff !important;
            border-color: #4285f4 !important;
          }

          .regenerate-content {
            display: flex !important;
            align-items: center !important;
            gap: 6px !important;

            .regenerate-icon {
              font-size: 16px !important;
              font-weight: bold !important;
            }
          }
        }
      }
    }

    // Action Buttons at Bottom
    .action-buttons {
      display: flex;
      justify-content: center;
      gap: 16px;
      padding-top: 24px;
      border-top: 1px solid #e5e7eb;

      ::ng-deep ava-button {
        .ava-button {
          min-width: 120px !important;
          padding: 12px 24px !important;
          font-size: 14px !important;
          font-weight: 500 !important;
        }

        // Proceed Button - Figma Gradient
        &[variant="primary"] .ava-button {
          background: linear-gradient(118deg, rgb(33, 90, 214) 55.27%, rgb(3, 189, 212)) !important;
          border: none !important;
          color: white !important;

          &:hover {
            background: linear-gradient(118deg, rgb(33, 90, 214) 55.27%, rgb(3, 189, 212)) !important;
            opacity: 0.9 !important;
          }
        }

        // Cancel Button - Figma Style
        &[variant="secondary"] .ava-button {
          background: transparent !important;
          border: 1px solid #d1d5db !important;
          color: linear-gradient(118deg, rgb(33, 90, 214) 55.27%, rgb(3, 189, 212))!important;
        }
      }
    }
  }

  .pill-buttons-container {
    display: flex;
    justify-content: center;
    margin-bottom: 32px;

    ::ng-deep ava-button {
      .ava-button {
        margin: 0 !important;
        min-width: 100px !important;
        padding: 10px 24px !important;
        font-size: 14px !important;
        font-weight: 500 !important;

        &.ava-button--primary {
          background: #4285f4 !important;
          color: #ffffff !important;
          border: 1px solid #4285f4 !important;
          z-index: 2;
          position: relative;
        }

        &.ava-button--secondary {
          background: #f8f9fa !important;
          color: #5f6368 !important;
          border: 1px solid #d1d5db !important;

          &:hover {
            background: #e8eaed !important;
            color: #5f6368 !important;
          }
        }
      }

      &:first-child .ava-button {
        border-radius: 20px 0 0 20px !important;
        border-right: none !important;
      }

      &:last-child .ava-button {
        border-radius: 0 20px 20px 0 !important;
      }
    }
  }

  .prompt-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .freeform-content {
    display: flex;
    flex-direction: column;
    height: 100%;

    .description-text {
      background: #f8f9fa;
      border: 1px solid #e8eaed;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 16px;
      color: #5f6368;
      font-size: 14px;
      line-height: 1.6;
      text-align: left;
      min-height: 120px;
    }

    .regenerate-container {
      margin-top: auto;
      display: flex;
      justify-content: flex-end;

      ::ng-deep ava-button {
        .ava-button {
          border-radius: 4px !important;
          font-size: 12px !important;
          padding: 8px 12px !important;
          background: transparent !important;
          color: #4285f4 !important;
          border: 1px solid #4285f4 !important;

          &:hover {
            background: #4285f4 !important;
            color: white !important;
          }

          .ava-button__icon {
            margin-right: 4px !important;
          }
        }
      }
    }
  }

  .template-content {
    max-height: 420px;
    overflow-y: auto;
    padding-right: 8px;

    /* Hide scrollbar but keep functionality */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */

    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Opera */
    }

    .template-form {
      .form-row {
        margin-bottom: 20px;

        &.single-field {
          .form-field {
            width: 100%;
          }
        }

        &.two-fields {
          display: flex;
          gap: 20px;

          .form-field {
            flex: 1;
          }
        }

        .form-field {
          display: flex;
          flex-direction: column;
          gap: 8px;

          label {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
          }

          input, textarea {
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            color: #374151;
            font-family: inherit;
            box-sizing: border-box;

            &:focus {
              outline: none;
              border-color: #3b82f6;
              box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }
          }

          input {
            height: 44px;
          }

          textarea {
            height: 72px;
            resize: none;
            overflow: hidden;
            line-height: 1.4;
            
          }
        }
      }

      .regenerate-container {
        display: flex;
        justify-content: flex-end;
        margin-top: 20px;
        padding-top: 8px;

        ::ng-deep ava-button {
          .ava-button {
            border-radius: 4px !important;
            font-size: 12px !important;
            padding: 8px 12px !important;
            background: transparent !important;
            color: #4285f4 !important;
            border: 1px solid #4285f4 !important;

            &:hover {
              background: #4285f4 !important;
              color: white !important;
            }

            .ava-button__icon {
              margin-right: 4px !important;
            }
          }
        }
      }
    }
  }

  .popup-footer-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 16px;
    border-top: 1px solid #e5e5e5;
    margin-top: auto;

    ::ng-deep ava-button {
      .ava-button {
        border-radius: 4px !important;
        padding: 10px 24px !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        min-width: 90px !important;

        &.ava-button--secondary {
          background: #f1f3f4 !important;
          color: #5f6368 !important;
          border: 1px solid #e8eaed !important;

          &:hover {
            background: #e8eaed !important;
          }
        }

        &.ava-button--primary {
          background: #4285f4 !important;
          color: white !important;
          border: 1px solid #4285f4 !important;

          &:hover {
            background: #3367d6 !important;
          }
        }
      }
    }
  }

  /* Popup button styling to match Figma design */
  ::ng-deep ava-popup {
    .popup-footer {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      padding: 20px 24px;
      border-top: 1px solid #e5e7eb;
      margin-top: 0;

      ava-button {
        .ava-button {
          border-radius: 6px !important;
          font-size: 14px !important;
          font-weight: 500 !important;
          padding: 10px 20px !important;
          min-width: 80px !important;
          height: 40px !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;

          &.ava-button--secondary {
            background: #ffffff !important;
            color: #374151 !important;
            border: 1px solid #d1d5db !important;

            &:hover {
              background: #f9fafb !important;
              border-color: #9ca3af !important;
            }
          }

          &.ava-button--primary {
            background: #4285f4 !important;
            color: #ffffff !important;
            border: 1px solid #4285f4 !important;

            &:hover {
              background: #3367d6 !important;
              border-color: #3367d6 !important;
            }
          }
        }
      }
    }

    /* Smaller popup styling for save confirmation */
    &[ng-reflect-popup-width="400px"] {
      .popup-footer {
        padding: 16px 20px;

        ava-button {
          .ava-button {
            min-width: 70px !important;
            padding: 8px 16px !important;
          }
        }
      }
    }
  }

  // Template Form Styles - White Container Box with Scroll (Figma Design)
  .template-form {
    width: 100%;
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 24px;
    margin: 16px 0;
    max-height: 400px;
    overflow-y: auto;
    text-align: left;

    /* Custom scrollbar styling */
    scrollbar-width: thin;
    scrollbar-color: #d1d5db #f9fafb;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f9fafb;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #d1d5db;
      border-radius: 3px;

      &:hover {
        background: #9ca3af;
      }
    }

    .fields-row {
      margin-bottom: 24px;
      width: 100%;
      text-align: left; // Left align the row

      .field-col {
        width: 100%;
        text-align: left; // Left align the column

        .form-field {
          margin-bottom: 20px;
          width: 100%;
          text-align: left; // Left align the field

          label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            text-align: left; // Explicitly left align labels

            .required {
              color: #ef4444;
              margin-left: 2px;
            }
          }

          input, textarea, select {
            width: 100%;
            padding: 12px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 14px;
            color: #111827;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #ffffff;
            box-sizing: border-box;
            text-align: left; // Left align input text

            &:focus {
              outline: none;
              border-color: #3b82f6;
              box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            &::placeholder {
              color: #9ca3af;
              text-align: left; // Left align placeholder text
            }
          }

          input {
            height: 44px;
          }

          textarea {
            resize: none;
            min-height: 100px;
            line-height: 1.5;
            text-align: left; // Left align textarea content

            /* Hide scrollbar but keep functionality */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */

            &::-webkit-scrollbar {
              display: none; /* Chrome, Safari, Opera */
            }
          }

          select {
            height: 44px;
            cursor: pointer;
          }
        }

        .role-dropdown-row {
          display: flex;
          gap: 16px;
          width: 100%;

          .role-field {
            flex: 1;
          }

          .prompt-type-field {
            flex: 1;
          }
        }
      }

      // Two column layout for Goal and Description
      &.two-column {
        display: flex;
        gap: 16px;

        .field-col {
          flex: 1;
        }
      }
    }
  }

  // Freeform Content Styles
  .freeform-content-wrapper {
    width: 100%;
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 24px;
    margin: 16px 0;
    text-align: left;
    position: relative;

    .form-field {
      margin-bottom: 24px;
      width: 100%;
      text-align: left; // Left align the field

      label {
        display: block;
        margin-bottom: 12px;
        font-size: 14px;
        font-weight: 500;
        color: #374151;
        text-align: left; // Left align label

        .required {
          color: #ef4444;
          margin-left: 2px;
        }
      }

      textarea {
        width: 100%;
        padding: 16px;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        font-size: 14px;
        color: #111827;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background: #f8f9fa;
        resize: none;
        height: 300px; // Fixed height instead of min-height
        box-sizing: border-box;
        line-height: 1.5;
        text-align: left; // Left align textarea content
        overflow: hidden; // Hide scrollbars completely for freeform

        &:focus {
          outline: none;
          border-color: #3b82f6;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
          background: #ffffff;
        }

        &::placeholder {
          color: #9ca3af;
          text-align: left; // Left align placeholder
        }
      }
    }

    .regenerate-button-wrapper {
      display: flex;
      justify-content: flex-end;
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid #f3f4f6;
      width: 100%;

      ::ng-deep ava-button {
        .ava-button {
          border-radius: 6px !important;
          font-size: 12px !important;
          padding: 8px 16px !important;
          border: 1px solid #4285f4 !important;
          color: #4285f4 !important;
          background: transparent !important;

          &:hover {
            background: #f0f4ff !important;
          }
        }
      }
    }
  }

  // Save confirmation popup specific styling (400px width popup)
  &[ng-reflect-popup-width="400px"] {
    .popup-footer {
      ::ng-deep ava-button {
        .ava-button {
          &.ava-button--primary {
            background: linear-gradient(118deg, rgb(33, 90, 214) 55.27%, rgb(3, 189, 212)) !important;
            color: #ffffff !important;
            border: 1px solid transparent !important;

            &:hover {
              background: linear-gradient(118deg, rgb(33, 90, 214) 55.27%, rgb(3, 189, 212)) !important;
              opacity: 0.9 !important;
              border-color: transparent !important;
            }
          }
        }
      }
    }
  }
}

// Save Confirmation Popup - Using standard AVA popup styling

// Global Primary Button Gradient - Applied to all primary buttons in this component
::ng-deep ava-button[variant="primary"] .ava-button {
  background: linear-gradient(118deg, rgb(33, 90, 214) 55.27%, rgb(3, 189, 212)) !important;
  border: none !important;
  color: white !important;

  &:hover {
    background: linear-gradient(118deg, rgb(33, 90, 214) 55.27%, rgb(3, 189, 212)) !important;
    opacity: 0.9 !important;
  }
}

// Specific targeting for confirmation popup Yes button
::ng-deep ava-popup .popup-footer .ava-button--primary {
  background: linear-gradient(118deg, rgb(33, 90, 214) 55.27%, rgb(3, 189, 212)) !important;
  border: none !important;
  color: white !important;

  &:hover {
    background: linear-gradient(118deg, rgb(33, 90, 214) 55.27%, rgb(3, 189, 212)) !important;
    opacity: 0.9 !important;
  }
}

// Final Save Popup Styles - Simple Single Column Layout (Figma Design)
.final-save-popup-content {
  padding: 24px;
  width: 450px;
  text-align: left; // Left align the entire popup content

  // Form Fields
  .form-field {
    margin-bottom: 20px;

    .field-label {
      display: block;
      font-size: 14px;
      font-weight: 500;
      color: #374151;
      margin-bottom: 8px;

      .required {
        color: #ef4444;
        margin-left: 2px;
      }
    }

    .form-input {
      width: 100%;
      padding: 12px 16px;
      border: 1px solid #d1d5db;
      border-radius: 8px;
      font-size: 14px;
      color: #374151;
      background: #f9fafb;
      box-sizing: border-box;

      &:focus {
        outline: none;
        border-color: #4285f4;
        background: white;
      }

      &::placeholder {
        color: #9ca3af;
      }
    }

    .form-textarea {
      width: 100%;
      padding: 12px 16px;
      border: 1px solid #d1d5db;
      border-radius: 8px;
      font-size: 14px;
      color: #374151;
      background: white;
      resize: vertical;
      min-height: 120px;
      box-sizing: border-box;
      font-family: inherit;

      &:focus {
        outline: none;
        border-color: #4285f4;
      }

      &::placeholder {
        color: #9ca3af;
      }
    }
  }

  // Form fields in final save popup
  .form-field {
    margin-bottom: 20px;
    text-align: left; // Left align field

    .field-label {
      display: block;
      font-size: 14px;
      font-weight: 500;
      color: #374151;
      margin-bottom: 8px;
      text-align: left; // Left align label

      .required {
        color: #ef4444;
        margin-left: 2px;
      }
    }

    .form-input, .form-textarea {
      width: 100%;
      padding: 12px 16px;
      border: 1px solid #d1d5db;
      border-radius: 8px;
      font-size: 14px;
      color: #374151;
      background: white;
      box-sizing: border-box;
      text-align: left; // Left align input text

      &:focus {
        outline: none;
        border-color: #4285f4;
        box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
      }

      &::placeholder {
        color: #9ca3af;
        text-align: left; // Left align placeholder
      }
    }

    .form-textarea {
      resize: vertical;
      min-height: 100px;
      font-family: inherit;
    }
  }

  .final-save-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;

    ::ng-deep ava-button {
      .ava-button {
        min-width: 80px !important;
        padding: 12px 24px !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        border-radius: 8px !important;
      }

      // Close Button - Secondary Style
      &[variant="secondary"] .ava-button {
        background: transparent !important;
        border: 1px solid #d1d5db !important;
        color: #6b7280 !important;

        &:hover {
          background: #f9fafb !important;
          border-color: #9ca3af !important;
        }
      }

      // Save Button - Primary Style (Figma Gradient)
      &[variant="primary"] .ava-button {
        background: linear-gradient(118deg, rgb(33, 90, 214) 55.27%, rgb(3, 189, 212)) !important;
        border: none !important;
        color: white !important;

        &:hover {
          background: linear-gradient(118deg, rgb(33, 90, 214) 55.27%, rgb(3, 189, 212)) !important;
          opacity: 0.9 !important;
        }
      }
    }
  }
}
