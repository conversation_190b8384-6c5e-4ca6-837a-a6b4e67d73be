.search-input-container {
  width: 100%;
  position: relative;
  
  .search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: var(--search-bg, #ffffff);
    border: 1px solid var(--search-border, #e1e5e9);
    border-radius: 0.5rem;
    transition: all 0.2s ease-in-out;
    overflow: hidden;
    
    &:hover {
      border-color: var(--search-border-hover, #c1c7cd);
    }
  }
  
  &.focused .search-input-wrapper {
    border-color: var(--search-border-focus, #8c65f7);
    box-shadow: 0 0 0 3px var(--search-focus-shadow, rgba(140, 101, 247, 0.1));
  }
  
  &.has-value .search-input-wrapper {
    border-color: var(--search-border-active, #8c65f7);
  }
  
  .search-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 0.75rem;
    color: var(--search-icon-color, #6c757d);
    transition: color 0.2s ease-in-out;
    
    i {
      font-size: 0.875rem;
      
      &.active {
        color: var(--search-icon-active, #8c65f7);
      }
    }
  }
  
  .search-input {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    padding: 0.75rem 0.5rem 0.75rem 0;
    font-size: 0.875rem;
    line-height: 1.25;
    color: var(--search-text-color, #212529);
    font-family: inherit;
    
    &::placeholder {
      color: var(--search-placeholder-color, #6c757d);
      opacity: 1;
    }
    
    &:disabled {
      color: var(--search-disabled-color, #6c757d);
      cursor: not-allowed;
      
      &::placeholder {
        color: var(--search-disabled-placeholder, #adb5bd);
      }
    }
  }
  
  .clear-button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem;
    margin-right: 0.25rem;
    background: transparent;
    border: none;
    border-radius: 0.25rem;
    color: var(--search-clear-color, #6c757d);
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    
    &:hover {
      background: var(--search-clear-hover-bg, #f8f9fa);
      color: var(--search-clear-hover-color, #495057);
    }
    
    &:focus {
      outline: 2px solid var(--search-focus-outline, #8c65f7);
      outline-offset: 1px;
    }
    
    i {
      font-size: 0.75rem;
    }
  }
  
  .result-count {
    margin-top: 0.5rem;
    padding: 0 0.75rem;
    font-size: 0.75rem;
    color: var(--search-result-count-color, #6c757d);
    line-height: 1.2;
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .search-input-container {
    .search-input-wrapper {
      background: var(--search-bg-dark, #2d3748);
      border-color: var(--search-border-dark, #4a5568);
      
      &:hover {
        border-color: var(--search-border-hover-dark, #718096);
      }
    }
    
    &.focused .search-input-wrapper {
      border-color: var(--search-border-focus-dark, #8c65f7);
      box-shadow: 0 0 0 3px var(--search-focus-shadow-dark, rgba(140, 101, 247, 0.2));
    }
    
    .search-icon {
      color: var(--search-icon-color-dark, #a0aec0);
      
      i.active {
        color: var(--search-icon-active-dark, #8c65f7);
      }
    }
    
    .search-input {
      color: var(--search-text-color-dark, #f7fafc);
      
      &::placeholder {
        color: var(--search-placeholder-color-dark, #a0aec0);
      }
      
      &:disabled {
        color: var(--search-disabled-color-dark, #718096);
        
        &::placeholder {
          color: var(--search-disabled-placeholder-dark, #4a5568);
        }
      }
    }
    
    .clear-button {
      color: var(--search-clear-color-dark, #a0aec0);
      
      &:hover {
        background: var(--search-clear-hover-bg-dark, #4a5568);
        color: var(--search-clear-hover-color-dark, #f7fafc);
      }
    }
    
    .result-count {
      color: var(--search-result-count-color-dark, #a0aec0);
    }
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .search-input-container {
    .search-input-wrapper {
      border-width: 2px;
    }
    
    &.focused .search-input-wrapper {
      box-shadow: 0 0 0 4px var(--search-focus-shadow-high-contrast, rgba(0, 0, 0, 0.3));
    }
    
    .clear-button:focus {
      outline-width: 3px;
    }
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .search-input-container {
    .search-input-wrapper,
    .search-icon i,
    .clear-button {
      transition: none;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .search-input-container {
    .search-input {
      font-size: 1rem; // Prevent zoom on iOS
    }
  }
}
