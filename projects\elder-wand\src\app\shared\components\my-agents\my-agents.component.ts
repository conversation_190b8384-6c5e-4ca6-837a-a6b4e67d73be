import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { IconComponent } from "@ava/play-comp-library";

interface AgentCardButton {
  label: string;
  iconName?: string;
  type?: 'primary' | 'secondary';
}

interface AgentCard {
  iconName: string;
  iconColor: string;
  title: string;
  description: string;
  buttons: AgentCardButton[];
}

@Component({
  selector: 'app-my-agents',
  templateUrl: './my-agents.component.html',
  styleUrls: ['./my-agents.component.scss'],
  imports: [IconComponent, CommonModule]
})
export class MyAgentsComponent {
  cards: AgentCard[] = [
    {
      iconName: 'bot',
      iconColor: 'black',
      title: 'Create Agent',
      description: 'Create a smart AI agent to streamline tasks, automate workflows, and boost efficiency effortlessly.',
      buttons: [
        { label: 'Create', iconName: 'plus', type: 'primary' }
      ]
    },
    {
      iconName: 'workflow',
      iconColor: 'black',
      title: 'Create Agentic Workflows',
      description: 'Build and manage advanced, multi-step automation flows to handle complex tasks with ease.',
      buttons: [
        { label: 'Create', iconName: 'plus', type: 'primary' }
      ]
    },
    {
      iconName: 'file-text',
      iconColor: 'black',
      title: 'Read & Update My List',
      description: 'Configure AI to read, update, and manage data across your systems seamlessly.',
      buttons: [
        { label: 'Read', iconName: 'book-open', type: 'secondary' },
        { label: 'Update', iconName: 'cloud-upload', type: 'primary' }
      ]
    },
    {
      iconName: 'wrench',
      iconColor: 'black',
      title: 'Create Tool',
      description: 'Configure AI to read, update, and manage data across your systems seamlessly.',
      buttons: [
        { label: 'Create', iconName: 'plus', type: 'primary' }
      ]
    },
    {
      iconName: 'shield-check',
      iconColor: 'black',
      title: 'Create Guardrail',
      description: 'Configure AI to read, update, and manage data across your systems seamlessly.',
      buttons: [
        { label: 'Create', iconName: 'plus', type: 'primary' }
      ]
    },
    {
      iconName: 'book-text',
      iconColor: 'black',
      title: 'Create Knowledge Base',
      description: 'Configure AI to read, update, and manage data across your systems seamlessly.',
      buttons: [
        { label: 'Create', iconName: 'plus', type: 'primary' }
      ]
    }
  ];

  onButtonClick(card: AgentCard, button: AgentCardButton): void {
    console.log('Button clicked:', {
      cardTitle: card.title,
      buttonLabel: button.label,
      buttonIcon: button.iconName,
      cardIcon: card.iconName
    });
    
    // Future functionality can be added here
    // Example: navigation, API calls, etc.
  }
}
