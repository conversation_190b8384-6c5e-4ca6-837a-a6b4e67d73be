import { Component, ChangeDetectionStrategy, input, output, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';

export type Theme = 'light' | 'dark' | 'system';

/**
 * Theme Toggle Component - Provides theme switching functionality
 * Supports light, dark, and system preference modes
 * 
 * Features:
 * - Three-state toggle (light/dark/system)
 * - Visual feedback with icons
 * - Accessibility compliant
 * - Smooth transitions
 */
@Component({
  selector: 'app-theme-toggle',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="theme-toggle-container">
      <button
        class="theme-toggle"
        (click)="toggleTheme()"
        [attr.aria-label]="themeLabel()"
        [title]="themeLabel()">
        
        <div class="theme-icon" [attr.data-theme]="theme()">
          @switch (theme()) {
            @case ('light') {
              <i class="fas fa-sun" aria-hidden="true"></i>
            }
            @case ('dark') {
              <i class="fas fa-moon" aria-hidden="true"></i>
            }
            @case ('system') {
              <i class="fas fa-desktop" aria-hidden="true"></i>
            }
          }
        </div>
        
        <div class="theme-indicator">
          <div class="indicator-dot" [attr.data-theme]="theme()"></div>
        </div>
      </button>
      
      <!-- Theme Options Dropdown (shown on long press or right click) -->
      @if (showOptions()) {
        <div class="theme-options" role="menu">
          <button
            class="theme-option"
            [class.active]="theme() === 'light'"
            (click)="selectTheme('light')"
            role="menuitem">
            <i class="fas fa-sun" aria-hidden="true"></i>
            <span>Light</span>
          </button>
          
          <button
            class="theme-option"
            [class.active]="theme() === 'dark'"
            (click)="selectTheme('dark')"
            role="menuitem">
            <i class="fas fa-moon" aria-hidden="true"></i>
            <span>Dark</span>
          </button>
          
          <button
            class="theme-option"
            [class.active]="theme() === 'system'"
            (click)="selectTheme('system')"
            role="menuitem">
            <i class="fas fa-desktop" aria-hidden="true"></i>
            <span>System</span>
          </button>
        </div>
      }
    </div>
  `,
  styleUrls: ['./theme-toggle.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ThemeToggleComponent {
  // Input properties
  readonly theme = input<Theme>('system');

  // Output events
  readonly themeChange = output<Theme>();

  // Component state
  private readonly _showOptions = signal<boolean>(false);
  private longPressTimer: any;

  // Public readonly signals
  readonly showOptions = this._showOptions.asReadonly();

  // Computed properties
  readonly themeLabel = computed(() => {
    const currentTheme = this.theme();
    const labels = {
      light: 'Switch to dark theme',
      dark: 'Switch to system theme',
      system: 'Switch to light theme'
    };
    return labels[currentTheme];
  });

  readonly nextTheme = computed((): Theme => {
    const current = this.theme();
    const cycle: Theme[] = ['light', 'dark', 'system'];
    const currentIndex = cycle.indexOf(current);
    return cycle[(currentIndex + 1) % cycle.length];
  });

  /**
   * Toggle to next theme in cycle
   */
  toggleTheme(): void {
    const next = this.nextTheme();
    this.themeChange.emit(next);
  }

  /**
   * Select specific theme
   */
  selectTheme(selectedTheme: Theme): void {
    this.themeChange.emit(selectedTheme);
    this._showOptions.set(false);
  }

  /**
   * Show theme options on long press
   */
  onMouseDown(): void {
    this.longPressTimer = setTimeout(() => {
      this._showOptions.set(true);
    }, 500);
  }

  /**
   * Cancel long press
   */
  onMouseUp(): void {
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }
  }

  /**
   * Handle context menu (right click)
   */
  onContextMenu(event: Event): void {
    event.preventDefault();
    this._showOptions.set(!this._showOptions());
  }

  /**
   * Close options when clicking outside
   */
  onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;
    if (!target.closest('.theme-toggle-container')) {
      this._showOptions.set(false);
    }
  }
}
