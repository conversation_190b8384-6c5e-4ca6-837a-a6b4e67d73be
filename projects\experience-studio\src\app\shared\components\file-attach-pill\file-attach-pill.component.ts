import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  Output,
  EventEmitter,
  ChangeDetectionStrategy,
  ViewEncapsulation,
  ElementRef,
  signal,
  HostListener
} from '@angular/core';
import { IconsComponent } from '../icons/icons.component';

export interface FileAttachOption {
  name: string;
  icon: string;
  value: string;
}

type IconColor = 'action' | 'danger' | 'disable' | 'neutralIcon' | 'success' | 'warning' | 'whiteIcon' | 'blue' | '';

@Component({
  selector: 'exp-file-attach-pill',
  standalone: true,
  imports: [CommonModule, IconsComponent],
  templateUrl: './file-attach-pill.component.html',
  styleUrls: ['./file-attach-pill.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
  host: {
    '[class.theme-dark]': 'currentTheme === "dark"',
    '[class.theme-light]': 'currentTheme === "light"'
  }
})
export class FileAttachPillComponent {
  @Input() options: FileAttachOption[] = [
    { name: 'From Computer', icon: 'awe_upload', value: 'computer' },
    { name: 'From Cloud', icon: 'awe_cloud_upload', value: 'cloud' },
    { name: 'From URL', icon: 'awe_link', value: 'url' }
  ];
  
  @Input() mainIcon = 'awe_attach_file';
  @Input() mainText = 'Attach File';
  @Input() currentTheme: 'light' | 'dark' = 'light';
  
  @Output() optionSelected = new EventEmitter<FileAttachOption>();
  
  isHovered = signal(false);
  isDropdownOpen = signal(false);
  
  constructor(private elementRef: ElementRef) {}
  
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    if (!this.elementRef.nativeElement.contains(event.target)) {
      this.isDropdownOpen.set(false);
    }
  }
  
  onMouseEnter(): void {
    this.isHovered.set(true);
  }

  onMouseLeave(): void {
    // Always set isHovered to false when mouse leaves
    this.isHovered.set(false);
    // Close dropdown after a small delay to allow moving to dropdown
    setTimeout(() => {
      if (!this.isMouseOverDropdown) {
        this.isDropdownOpen.set(false);
      }
    }, 100);
  }

  // Track if mouse is over dropdown
  isMouseOverDropdown = false;

  onDropdownMouseEnter(): void {
    this.isMouseOverDropdown = true;
  }

  onDropdownMouseLeave(): void {
    this.isMouseOverDropdown = false;
    this.isDropdownOpen.set(false);
    this.isHovered.set(false);
  }
  
  toggleDropdown(event: Event): void {
    event.stopPropagation();
    this.isDropdownOpen.update(value => !value);
  }
  
  selectOption(option: FileAttachOption, event: Event): void {
    event.stopPropagation();
    this.optionSelected.emit(option);
    this.isDropdownOpen.set(false);
    this.isHovered.set(false);
  }

  get iconColor(): IconColor {
    return this.currentTheme === 'dark' ? 'whiteIcon' : 'neutralIcon';
  }
}
