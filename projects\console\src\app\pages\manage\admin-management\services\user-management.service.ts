import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { environment } from 'projects/console/src/environments/environment';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class UserManagementService {
  private apiAuthUrl = environment.consoleApiAuthUrl;

  private http = inject(HttpClient); 

  getAllUsers() {
    const url = `${this.apiAuthUrl}/user/mgmt`;
    return this.http.get(url);
  }

  getAllRoles() {
    const url = `${this.apiAuthUrl}/roles`;
    return this.http.get(url);
  }
}
