import { Injectable, signal, computed, inject, DestroyRef } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BehaviorSubject, Observable, combineLatest, of } from 'rxjs';
import { map, catchError, shareReplay, startWith } from 'rxjs/operators';
import { RecentProjectService, Project } from './recent-project-services/recent-project.service';
import { ToastService } from './toast.service';
import { CardOption, EnhancedCardOption, PROJECT_TYPES } from '../models/recent-creation.model';

/**
 * Service for managing project data with caching, error handling, and state management
 * Provides a centralized way to access and manage project information
 */
@Injectable({
  providedIn: 'root'
})
export class ProjectDataService {
  private readonly destroyRef = inject(DestroyRef);
  private readonly recentProjectService = inject(RecentProjectService);
  private readonly toastService = inject(ToastService);
  
  // Loading and error states
  private readonly _isLoading = signal<boolean>(false);
  private readonly _hasError = signal<boolean>(false);
  private readonly _errorMessage = signal<string | null>(null);
  
  // Project data cache
  private readonly _projectsCache = signal<Project[]>([]);
  private readonly _lastFetchTime = signal<number>(0);
  
  // Cache configuration
  private readonly CACHE_DURATION = 2 * 60 * 1000; // 2 minutes
  private readonly MAX_PROJECTS = 12;
  
  // Public readonly signals
  readonly isLoading = this._isLoading.asReadonly();
  readonly hasError = this._hasError.asReadonly();
  readonly errorMessage = this._errorMessage.asReadonly();
  
  // Computed project data
  readonly allProjects = computed(() => this.mapProjectsToCardOptions(this._projectsCache()));
  readonly recentProjects = computed(() => this.allProjects().slice(0, 4));
  
  // Project statistics
  readonly projectStats = computed(() => {
    const projects = this._projectsCache();
    const stats = {
      total: projects.length,
      byType: {} as Record<string, number>,
      lastModified: null as string | null
    };
    
    projects.forEach(project => {
      if (project.project_type) {
        stats.byType[project.project_type] = (stats.byType[project.project_type] || 0) + 1;
      }
    });
    
    if (projects.length > 0) {
      const sorted = [...projects].sort((a, b) => 
        new Date(b.last_modified).getTime() - new Date(a.last_modified).getTime()
      );
      stats.lastModified = sorted[0].last_modified;
    }
    
    return stats;
  });
  
  constructor() {
    // Auto-refresh projects when cache expires
    this.setupAutoRefresh();
  }
  
  /**
   * Load projects with caching and error handling
   */
  loadProjects(forceRefresh: boolean = false): Observable<CardOption[]> {
    const now = Date.now();
    const cacheValid = (now - this._lastFetchTime()) < this.CACHE_DURATION;
    
    if (!forceRefresh && cacheValid && this._projectsCache().length > 0) {
      return of(this.allProjects());
    }
    
    this._isLoading.set(true);
    this._hasError.set(false);
    this._errorMessage.set(null);
    
    return this.recentProjectService.getUserProjects('<EMAIL>', this.MAX_PROJECTS).pipe(
      map(response => {
        if (response.status_code === 200 && Array.isArray(response.projects)) {
          this._projectsCache.set(response.projects);
          this._lastFetchTime.set(now);
          this._isLoading.set(false);
          return this.allProjects();
        } else {
          throw new Error('Invalid response format');
        }
      }),
      catchError(error => {
        this._isLoading.set(false);
        this._hasError.set(true);
        const errorMsg = error?.message || 'Failed to load projects';
        this._errorMessage.set(errorMsg);
        this.toastService.error(`Failed to load projects: ${errorMsg}`);
        return of([]);
      }),
      shareReplay(1),
      takeUntilDestroyed(this.destroyRef)
    );
  }
  
  /**
   * Get project by ID
   */
  getProjectById(projectId: string): CardOption | null {
    const projects = this.allProjects();
    return projects.find(project => project.id === projectId) || null;
  }
  
  /**
   * Get projects by type
   */
  getProjectsByType(projectType: string): CardOption[] {
    return this.allProjects().filter(project => project.type === projectType);
  }
  
  /**
   * Search projects
   */
  searchProjects(searchTerm: string): CardOption[] {
    if (!searchTerm.trim()) {
      return this.allProjects();
    }
    
    const term = searchTerm.toLowerCase();
    return this.allProjects().filter(project =>
      project.heading.toLowerCase().includes(term) ||
      project.description.toLowerCase().includes(term) ||
      project.type.toLowerCase().includes(term)
    );
  }
  
  /**
   * Refresh projects data
   */
  refreshProjects(): Observable<CardOption[]> {
    return this.loadProjects(true);
  }
  
  /**
   * Clear cache
   */
  clearCache(): void {
    this._projectsCache.set([]);
    this._lastFetchTime.set(0);
    this._hasError.set(false);
    this._errorMessage.set(null);
  }
  
  /**
   * Check if cache is valid
   */
  isCacheValid(): boolean {
    const now = Date.now();
    return (now - this._lastFetchTime()) < this.CACHE_DURATION;
  }
  
  /**
   * Get cache age in milliseconds
   */
  getCacheAge(): number {
    return Date.now() - this._lastFetchTime();
  }
  
  /**
   * Map projects to card options with enhanced data
   */
  private mapProjectsToCardOptions(projects: Project[]): CardOption[] {
    return projects
      .filter(project => project.project_type !== null && project.project_type !== undefined)
      .map(project => ({
        id: project.project_id,
        heading: project.project_name,
        description: this.truncateDescription(project.project_description.replace(/^"|"$/g, '')),
        type: project.project_type!.toLowerCase(),
        timestamp: this.recentProjectService.formatDate(project.last_modified),
      }));
  }
  
  /**
   * Truncate description to a reasonable length
   */
  private truncateDescription(description: string): string {
    const words = description.split(' ');
    return words.length > 10 ? words.slice(0, 10).join(' ') + '...' : description;
  }
  
  /**
   * Setup automatic cache refresh
   */
  private setupAutoRefresh(): void {
    // Auto-refresh every 5 minutes when cache is stale
    setInterval(() => {
      if (!this.isCacheValid() && !this._isLoading()) {
        this.loadProjects(true).subscribe();
      }
    }, 5 * 60 * 1000);
  }
}
