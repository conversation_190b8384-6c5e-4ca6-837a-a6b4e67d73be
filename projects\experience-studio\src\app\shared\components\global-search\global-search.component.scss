@use '../../../../../../public/assets/styles/mixins' as mixins;

// Global Search Component Styles
.global-search {
  position: relative;
  width: 100%;
  max-width: 600px;

  // CSS Custom Properties
  --search-bg: #f8f9fa;
  --search-bg-dark: #2d3748;
  --search-border: #e1e5e9;
  --search-border-dark: #4a5568;
  --search-focus: #8c65f7;
  --suggestion-bg: #ffffff;
  --suggestion-bg-dark: #1a202c;
  --suggestion-hover: #f8f9fa;
  --suggestion-hover-dark: #2d3748;

  &[data-theme="dark"] {
    --search-bg: var(--search-bg-dark);
    --search-border: var(--search-border-dark);
    --suggestion-bg: var(--suggestion-bg-dark);
    --suggestion-hover: var(--suggestion-hover-dark);
  }

  .search-input-container {
    position: relative;
    display: flex;
    align-items: center;
    background: var(--search-bg);
    border: 1px solid var(--search-border);
    border-radius: 0.75rem;
    transition: all 0.2s ease;

    &:focus-within {
      border-color: var(--search-focus);
      box-shadow: 0 0 0 3px rgba(140, 101, 247, 0.1);
    }

    .search-icon {
      position: absolute;
      left: 1rem;
      color: var(--text-secondary);
      font-size: 1rem;
      z-index: 1;
    }

    .search-input {
      width: 100%;
      padding: 0.875rem 1rem 0.875rem 2.75rem;
      background: transparent;
      border: none;
      font-size: 0.875rem;
      color: var(--text-primary);
      outline: none;

      &::placeholder {
        color: var(--text-secondary);
      }
    }

    .clear-search {
      position: absolute;
      right: 0.75rem;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 1.5rem;
      height: 1.5rem;
      background: transparent;
      border: none;
      border-radius: 50%;
      color: var(--text-secondary);
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: var(--suggestion-hover);
        color: var(--text-primary);
      }
    }

    .search-loading {
      position: absolute;
      right: 0.75rem;
      color: var(--search-focus);
      font-size: 0.875rem;
    }
  }

  &.focused {
    .search-input-container {
      border-color: var(--search-focus);
      box-shadow: 0 0 0 3px rgba(140, 101, 247, 0.1);
    }
  }

  .search-suggestions {
    position: absolute;
    top: calc(100% + 0.5rem);
    left: 0;
    right: 0;
    background: var(--suggestion-bg);
    border: 1px solid var(--search-border);
    border-radius: 0.75rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    max-height: 400px;
    overflow-y: auto;
    z-index: 1000;
    animation: slideDown 0.2s ease-out;

    .suggestion-section {
      &:not(:last-child) {
        border-bottom: 1px solid var(--search-border);
      }

      .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.75rem 1rem 0.5rem;
        color: var(--text-secondary);
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;

        .clear-history {
          background: transparent;
          border: none;
          color: var(--text-secondary);
          cursor: pointer;
          padding: 0.25rem;
          border-radius: 0.25rem;
          transition: all 0.2s ease;

          &:hover {
            background: var(--suggestion-hover);
            color: var(--text-primary);
          }
        }
      }

      .suggestion-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        width: 100%;
        padding: 0.75rem 1rem;
        background: transparent;
        border: none;
        text-align: left;
        cursor: pointer;
        transition: all 0.2s ease;
        border-radius: 0;

        &:hover,
        &.highlighted {
          background: var(--suggestion-hover);
        }

        &:first-of-type {
          border-top-left-radius: 0.75rem;
          border-top-right-radius: 0.75rem;
        }

        &:last-of-type {
          border-bottom-left-radius: 0.75rem;
          border-bottom-right-radius: 0.75rem;
        }

        .suggestion-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 2rem;
          height: 2rem;
          background: rgba(140, 101, 247, 0.1);
          border-radius: 0.5rem;
          color: var(--search-focus);
          font-size: 0.875rem;
          flex-shrink: 0;
        }

        .suggestion-content {
          flex: 1;
          min-width: 0;

          .suggestion-text {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 0.125rem;

            mark {
              background: rgba(140, 101, 247, 0.2);
              color: var(--search-focus);
              padding: 0;
              border-radius: 0.125rem;
            }
          }

          .suggestion-category {
            display: block;
            font-size: 0.75rem;
            color: var(--text-secondary);
          }
        }

        .suggestion-meta {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          flex-shrink: 0;

          .rating {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.75rem;
            color: var(--text-secondary);

            i {
              color: #fbbf24;
            }
          }
        }

        &.recent-search {
          .suggestion-icon {
            background: rgba(107, 114, 128, 0.1);
            color: var(--text-secondary);
          }
        }

        &.quick-action {
          .suggestion-icon {
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
          }
        }

        &.search-result {
          .suggestion-icon {
            background: rgba(140, 101, 247, 0.1);
            color: var(--search-focus);
          }
        }
      }
    }

    .no-results {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 2rem 1rem;
      text-align: center;

      .no-results-icon {
        font-size: 2rem;
        color: var(--text-secondary);
        margin-bottom: 0.75rem;
        opacity: 0.5;
      }

      .no-results-content {
        .no-results-title {
          font-size: 0.875rem;
          font-weight: 600;
          color: var(--text-primary);
          margin-bottom: 0.25rem;
        }

        .no-results-subtitle {
          font-size: 0.75rem;
          color: var(--text-secondary);
        }
      }
    }
  }

  &.has-results {
    .search-input-container {
      border-bottom-left-radius: 0.375rem;
      border-bottom-right-radius: 0.375rem;
    }
  }
}

// Animations
@keyframes slideDown {
  0% {
    opacity: 0;
    transform: translateY(-0.5rem);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .global-search {
    .search-input-container {
      .search-input {
        padding: 0.75rem 1rem 0.75rem 2.5rem;
        font-size: 0.875rem;
      }

      .search-icon {
        left: 0.875rem;
        font-size: 0.875rem;
      }
    }

    .search-suggestions {
      max-height: 300px;

      .suggestion-section .suggestion-item {
        padding: 0.625rem 0.875rem;

        .suggestion-icon {
          width: 1.75rem;
          height: 1.75rem;
          font-size: 0.75rem;
        }

        .suggestion-content {
          .suggestion-text {
            font-size: 0.8125rem;
          }

          .suggestion-category {
            font-size: 0.6875rem;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .global-search {
    .search-suggestions {
      left: -0.5rem;
      right: -0.5rem;
      max-height: 250px;
    }
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .global-search {
    .search-input-container {
      border-width: 2px;
    }

    .search-suggestions {
      border-width: 2px;

      .suggestion-section .suggestion-item {
        border: 1px solid transparent;

        &:hover,
        &.highlighted {
          border-color: var(--search-focus);
        }
      }
    }
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .global-search {
    * {
      transition: none !important;
      animation: none !important;
    }
  }
}
