.layout {
    display: flex;
    height: 85vh;
    width: 100vw;
    overflow: hidden;
    position:fixed;
  }
  
  .left-pane {
    width: 300px;
    min-width: 300px;
    transition: all 0.3s ease;
    background-color: #f8f9fb;
    border-right: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
  
    &.collapsed {
      width: 40px;
      min-width: 40px;
  
      .left-content {
        display: none;
        padding:0.5rem;
      }
  
      .left-header {
        justify-content: center;
      }
    }
  
    .left-header {
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 8px;
      background: #ffffff;
      border-bottom: 1px solid #e0e0e0;
      z-index: 1;
  
      .icon {
        cursor: pointer;
        width: 24px;
        height: 24px;
      }
    }
  }
  
  
  .center-pane {
    flex: 1;
    transition: all 0.3s ease;
    background: #fff;
    //padding: 1rem;
    overflow-y: auto;
    border-right: 1px solid #e0e0e0;
  
    &.full-width {
      border-right: none;
    }
  }
  
  
  .right-pane {
    width: 30%;
    min-width: 320px;
    //max-width: 400px;
    background-color: #fafafa;
    //padding: 1rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
  }

  .right-header {
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 8px;
    background-color: #fafafa;
    border-bottom: 1px solid #e0e0e0;
    z-index: 1;
  }
  