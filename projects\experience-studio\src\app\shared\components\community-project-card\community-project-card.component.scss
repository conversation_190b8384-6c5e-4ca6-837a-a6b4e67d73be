@use '../../../../../../public/assets/styles/mixins' as mixins;

// Community Project Card Component Styles
.community-project-card {
  background: var(--card-bg);
  border: 1px solid var(--border-light);
  border-radius: 0.75rem;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;

  // CSS Custom Properties
  --card-hover-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
  --card-hover-shadow-dark: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
  --overlay-bg: rgba(0, 0, 0, 0.7);
  --favorite-color: #ef4444;
  --rating-color: #fbbf24;

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--card-hover-shadow);
    border-color: var(--primary-purple);

    .project-thumbnail .thumbnail-overlay {
      opacity: 1;
    }
  }

  &.favorited {
    .project-thumbnail .thumbnail-overlay .favorite-btn {
      color: var(--favorite-color);
    }
  }

  // Grid view styles
  &[data-view="grid"] {
    display: flex;
    flex-direction: column;
    height: 100%;

    .project-thumbnail {
      height: 200px;
      position: relative;
      overflow: hidden;

      .thumbnail-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }

      .thumbnail-placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--nav-item-hover);
        color: var(--text-secondary);
        font-size: 2rem;
      }

      .thumbnail-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--overlay-bg);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
        opacity: 0;
        transition: all 0.3s ease;

        &.visible {
          opacity: 1;
        }

        .overlay-action {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.75rem 1rem;
          background: rgba(255, 255, 255, 0.9);
          border: none;
          border-radius: 0.5rem;
          color: var(--text-primary);
          font-size: 0.875rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background: white;
            transform: translateY(-1px);
          }

          &.preview-btn {
            background: var(--primary-purple);
            color: white;

            &:hover {
              background: #7c3aed;
            }
          }

          &.favorite-btn {
            width: 2.5rem;
            height: 2.5rem;
            padding: 0;
            border-radius: 50%;
            justify-content: center;

            &.active {
              background: var(--favorite-color);
              color: white;

              &:hover {
                background: #dc2626;
              }
            }
          }
        }
      }

      .project-type-overlay {
        position: absolute;
        top: 0.75rem;
        left: 0.75rem;
        z-index: 2;
      }

      .difficulty-badge {
        position: absolute;
        top: 0.75rem;
        right: 0.75rem;
        padding: 0.25rem 0.5rem;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        font-size: 0.75rem;
        font-weight: 500;
        border-radius: 0.25rem;
        z-index: 2;

        &[data-difficulty="beginner"] {
          background: rgba(16, 185, 129, 0.9);
        }

        &[data-difficulty="intermediate"] {
          background: rgba(245, 158, 11, 0.9);
        }

        &[data-difficulty="advanced"] {
          background: rgba(239, 68, 68, 0.9);
        }
      }
    }

    .project-content {
      flex: 1;
      padding: 1.25rem;
      display: flex;
      flex-direction: column;
      gap: 0.75rem;

      .project-header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        gap: 0.75rem;

        .project-title {
          font-size: 1.125rem;
          font-weight: 600;
          color: var(--text-primary);
          margin: 0;
          line-height: 1.3;
          flex: 1;
        }

        .project-actions {
          display: flex;
          gap: 0.25rem;
          flex-shrink: 0;

          .action-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 2rem;
            height: 2rem;
            background: transparent;
            border: none;
            border-radius: 0.375rem;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
              background: var(--nav-item-hover);
              color: var(--text-primary);
            }
          }
        }
      }

      .project-description {
        font-size: 0.875rem;
        color: var(--text-secondary);
        line-height: 1.5;
        margin: 0;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .project-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;

        .project-tag {
          padding: 0.25rem 0.5rem;
          background: rgba(140, 101, 247, 0.1);
          color: var(--primary-purple);
          font-size: 0.75rem;
          font-weight: 500;
          border-radius: 0.375rem;
          border: 1px solid rgba(140, 101, 247, 0.2);
        }

        .more-tags {
          padding: 0.25rem 0.5rem;
          background: var(--nav-item-hover);
          color: var(--text-secondary);
          font-size: 0.75rem;
          font-weight: 500;
          border-radius: 0.375rem;
        }
      }

      .creator-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding-top: 0.75rem;
        border-top: 1px solid var(--border-light);

        .creator-avatar {
          width: 2rem;
          height: 2rem;
          border-radius: 50%;
          overflow: hidden;
          flex-shrink: 0;

          .avatar-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .creator-details {
          flex: 1;
          min-width: 0;

          .creator-name {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 0.125rem;
          }

          .project-date {
            font-size: 0.75rem;
            color: var(--text-secondary);
          }
        }
      }

      .project-stats {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-top: auto;

        .stat-item {
          display: flex;
          align-items: center;
          gap: 0.375rem;
          font-size: 0.75rem;
          color: var(--text-secondary);

          &.rating {
            color: var(--rating-color);

            i {
              color: var(--rating-color);
            }
          }

          .stat-value {
            font-weight: 500;
          }
        }
      }
    }
  }

  // List view styles
  &[data-view="list"] {
    display: grid;
    grid-template-columns: 200px 1fr;
    gap: 1.25rem;
    align-items: start;

    .project-thumbnail {
      height: 140px;
      border-radius: 0.5rem;
      overflow: hidden;
    }

    .project-content {
      padding: 0.75rem 0;

      .list-view-extras {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid var(--border-light);

        .project-summary {
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 1rem;

          .summary-stats {
            display: flex;
            gap: 1rem;

            .summary-item {
              font-size: 0.875rem;
              color: var(--text-secondary);

              strong {
                color: var(--text-primary);
              }
            }
          }

          .quick-actions {
            display: flex;
            gap: 0.75rem;

            .quick-action-btn {
              display: flex;
              align-items: center;
              gap: 0.5rem;
              padding: 0.5rem 0.75rem;
              border: 1px solid var(--border-light);
              border-radius: 0.375rem;
              font-size: 0.875rem;
              font-weight: 500;
              cursor: pointer;
              transition: all 0.2s ease;

              &.primary {
                background: var(--primary-purple);
                border-color: var(--primary-purple);
                color: white;

                &:hover {
                  background: #7c3aed;
                }
              }

              &.secondary {
                background: transparent;
                color: var(--text-secondary);

                &:hover {
                  background: var(--nav-item-hover);
                  color: var(--text-primary);
                }
              }
            }
          }
        }
      }
    }
  }
}

// Dark theme overrides
@media (prefers-color-scheme: dark) {
  .community-project-card {
    --card-hover-shadow: var(--card-hover-shadow-dark);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .community-project-card {
    &[data-view="list"] {
      grid-template-columns: 1fr;
      gap: 1rem;

      .project-thumbnail {
        height: 160px;
      }

      .project-content .list-view-extras .project-summary {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;

        .summary-stats {
          justify-content: space-between;
        }

        .quick-actions {
          justify-content: stretch;

          .quick-action-btn {
            flex: 1;
            justify-content: center;
          }
        }
      }
    }
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .community-project-card {
    border-width: 2px;

    &:hover {
      border-width: 3px;
    }

    .project-content .project-tags .project-tag {
      border-width: 2px;
    }
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .community-project-card {
    * {
      transition: none !important;
    }

    &:hover {
      transform: none;
    }

    .project-thumbnail .thumbnail-image {
      transition: none !important;
    }
  }
}
