.project-type-tag {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  border-radius: 0.375rem;
  border: 1px solid;
  font-weight: 500;
  font-family: inherit;
  white-space: nowrap;
  transition: all 0.2s ease-in-out;
  
  // Size variants
  &.small {
    padding: 0.125rem 0.375rem;
    font-size: 0.75rem;
    line-height: 1.2;
    
    .tag-icon {
      font-size: 0.625rem;
    }
  }
  
  &.medium {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    line-height: 1.25;
    
    .tag-icon {
      font-size: 0.75rem;
    }
  }
  
  &.large {
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
    
    .tag-icon {
      font-size: 0.875rem;
    }
  }
  
  // Icon styling
  .tag-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    
    i {
      margin: 0;
    }
  }
  
  // Text styling
  .tag-text {
    font-weight: inherit;
    letter-spacing: 0.025em;
  }
  
  // Hover effects for interactive tags
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  // Focus styles for accessibility
  &:focus {
    outline: 2px solid var(--focus-color, #007bff);
    outline-offset: 2px;
  }
  
  // Dark theme support
  @media (prefers-color-scheme: dark) {
    &.filled {
      // Slightly reduce opacity in dark mode for better contrast
      opacity: 0.9;
    }
    
    &.outlined,
    &.subtle {
      // Ensure good contrast in dark mode
      border-color: currentColor;
    }
  }
  
  // High contrast mode support
  @media (prefers-contrast: high) {
    border-width: 2px;
    font-weight: 600;
  }
  
  // Reduced motion support
  @media (prefers-reduced-motion: reduce) {
    transition: none;
    
    &:hover {
      transform: none;
    }
  }
}

// Specific project type color overrides if needed
.project-type-tag {
  &[data-project-type="wireframe_generation"] {
    // Custom styling for wireframe generation if needed
  }
  
  &[data-project-type="app_generation"] {
    // Custom styling for app generation if needed
  }
}

// Container for multiple tags
.project-type-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.375rem;
  align-items: center;
}

// Responsive adjustments
@media (max-width: 768px) {
  .project-type-tag {
    &.large {
      padding: 0.25rem 0.5rem;
      font-size: 0.875rem;
    }
    
    &.medium {
      padding: 0.1875rem 0.375rem;
      font-size: 0.8125rem;
    }
  }
}
