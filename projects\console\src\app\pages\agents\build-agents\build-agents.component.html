<div class="build-agents-container">
  <!-- Header Navigation -->
  <div class="header-nav">
    <!-- Main Content Area -->
    <div class="main-content" [class.execute-mode]="isExecuteMode">
      <!-- Full Width Canvas Area -->
      <div class="canvas-area" [class.execute-mode]="isExecuteMode">
        <!-- Configure Agent Floating Panel - Visible in both build and execute modes -->
        <div
          class="configure-agent-panel"
          [ngClass]="{ 'p-3': !isSidebarCollapsed }"
          [class.collapsed]="isSidebarCollapsed"
        >
          <div class="panel-header" (click)="toggleSidebar()">
            <h3>Configure Agent</h3>
            <ava-icon
              [iconName]="isSidebarCollapsed ? 'ChevronDown' : 'ChevronUp'"
              iconSize="16"
              iconColor="var(--text-secondary)"
            >
            </ava-icon>
          </div>

          <!-- Panel Content -->
          <div class="panel-content" [class.hidden]="isSidebarCollapsed">
            <!-- Tool Items Section -->
            <div class="tools-section">
              <!-- Ava Tabs with Custom PNG Icons -->
              <div class="custom-tabs-container">
                <app-custom-tabs
                  [tabs]="customTabs"
                  [activeTab]="activeTab"
                  [variant]="'icon'"
                  (tabChange)="onCustomTabChange($event)"
                  class="builder-custom-tabs"
                >
                </app-custom-tabs>
              </div>

              <!-- Search Section -->
              <div class="search-section">
                <form [formGroup]="searchForm">
                  <ava-textbox
                    [placeholder]="'Search ' + activeTab"
                    hoverEffect="glow"
                    pressedEffect="solid"
                    formControlName="search"
                  >
                    <ava-icon
                      slot="icon-start"
                      iconName="search"
                      [iconSize]="16"
                      iconColor="var(--color-brand-primary)"
                    >
                    </ava-icon>
                  </ava-textbox>
                </form>
              </div>

              <!-- Tool Items List -->
              <div class="tools-list">
                <!-- Show tools when available -->
                <div
                  *ngFor="let tool of filteredTools"
                  class="tool-item"
                  draggable="true"
                  (dragstart)="onDragStart($event, tool)"
                >
                  <!-- Header with icon, name and user count in one line -->
                  <div class="tool-header">
                    <div class="tool-icon-box">
                      <!-- Use Lucide icons for all types including prompts -->
                      <ava-icon
                        *ngIf="tool.type === 'prompt'"
                        iconName="FileText"
                        iconSize="20"
                        iconColor="var(--color-brand-primary)"
                      >
                      </ava-icon>
                      <ava-icon
                        *ngIf="tool.type === 'model'"
                        iconName="Box"
                        iconSize="20"
                        iconColor="var(--color-brand-primary)"
                      >
                      </ava-icon>
                      <ava-icon
                        *ngIf="tool.type === 'knowledge'"
                        iconName="BookOpen"
                        iconSize="20"
                        iconColor="var(--color-brand-primary)"
                      >
                      </ava-icon>
                      <ava-icon
                        *ngIf="tool.type === 'tool'"
                        iconName="Wrench"
                        iconSize="20"
                        iconColor="var(--color-brand-primary)"
                      >
                      </ava-icon>
                      <ava-icon
                        *ngIf="tool.type === 'guardrail'"
                        iconName="Swords"
                        iconSize="20"
                        iconColor="var(--color-brand-primary)"
                      >
                      </ava-icon>
                    </div>
                    <h4 class="tool-name">{{ tool.name }}</h4>
                    <div class="tool-count">
                      <ava-icon
                        iconName="Users"
                        iconSize="16"
                        iconColor="#9CA3AF"
                      >
                      </ava-icon>
                      <span class="count-text">120</span>
                    </div>
                  </div>

                  <!-- Description -->
                  <p class="tool-description">{{ tool.description }}</p>

                  <!-- Preview button -->
                  <div class="tool-actions">
                    <ava-button
                      label="Preview"
                      size="small"
                      [pill]="true"
                      variant="secondary"
                      (userClick)="onItemPreview(tool)"
                      class="preview-btn"
                    >
                    </ava-button>
                  </div>
                </div>

                <!-- Show no results message when search returns empty and search query exists -->
                <div
                  *ngIf="
                    filteredTools.length === 0 &&
                    searchQuery &&
                    searchQuery.trim().length > 0
                  "
                  class="no-results-message"
                >
                  <div class="no-results-content">
                    <ava-icon
                      iconName="Search"
                      iconSize="24"
                      iconColor="#9CA3AF"
                    >
                    </ava-icon>
                    <p>
                      No {{ activeTab }} found matching your search criteria
                    </p>
                  </div>
                </div>
              </div>

              <!-- Create New Item Button -->
              <div class="create-tool-section">
                <ava-button
                  [label]="'Create New ' + getActiveTabLabel()"
                  variant="primary"
                  [customStyles]="{
                    background:
                      'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
                    '--button-effect-color': '33, 90, 214',
                    'border-radius': '8px',
                  }"
                  size="large"
                  iconName="Plus"
                  iconColor="white"
                  (userClick)="onCreateNewItem()"
                  [width]="'100%'"
                >
                </ava-button>
              </div>
            </div>
          </div>
        </div>

        <div class="editor-canvas">
          <app-canvas-board
            [nodes]="canvasNodes"
            [edges]="canvasEdges"
            [navigationHints]="[]"
            [fallbackMessage]="
              'Drag tools from the left panel to build your agent'
            "
            [primaryButtonText]="primaryButtonText"
            [showToolbar]="true"
            [enableConnections]="true"
            [enablePan]="false"
            [enableZoom]="false"
            [isExecuteMode]="isExecuteMode"
            [mouseInteractionsEnabled]="!isExecuteMode"
            [showHeaderInputs]="true"
            [initialAgentName]="agentName"
            [initialAgentDetails]="agentDetail"
            [initialMetadata]="agentMetadata"
            [inputFieldsConfig]="{
              agentName: {
                enabled: !isFieldsDisabled,
                placeholder: 'Agent Name',
                required: true,
              },
              metadata: {
                enabled: !isFieldsDisabled,
                label: 'Metadata Info',
                statusText: {
                  saved: 'Metadata Information saved',
                  notSaved: 'Metadata Information not saved',
                },
              },
              agentDetails: {
                enabled: true,
                label: 'Agent Details',
                namePlaceholder: 'Enter agent name',
                detailPlaceholder: 'Enter use case details and description',
              },
              agentTypeTag: {
                enabled: true,
                value: currentAgentType,
                showInAgentsBuilderOnly: true,
              },
            }"
            (canvasDropped)="onCanvasDropped($event)"
            (nodeSelected)="onNodeSelected($event)"
            (nodeDoubleClicked)="onNodeDoubleClicked($event)"
            (nodeMoved)="onNodeMoved($event)"
            (nodeRemoved)="onDeleteNode($event)"
            (connectionCreated)="onConnectionCreated($event)"
            (stateChanged)="onCanvasStateChanged($event)"
            (agentNameChanged)="onAgentNameChanged($event)"
            (metadataChanged)="onMetadataChanged($event)"
            (agentDetailsChanged)="onAgentDetailsChanged($event)"
            (primaryButtonClicked)="onPrimaryButtonClick()"
          >
            <!-- Node template for rendering agent nodes -->
            <ng-template
              #nodeTemplate
              let-node
              let-selected="selected"
              let-onDelete="onDelete"
              let-onMove="onMove"
              let-onSelect="onSelect"
              let-onDoubleClick="onDoubleClick"
              let-onStartConnection="onStartConnection"
              let-mouseInteractionsEnabled="mouseInteractionsEnabled"
              let-canvasMode="canvasMode"
            >
              <app-build-agent-node
                [node]="node"
                [selected]="selected"
                [mouseInteractionsEnabled]="mouseInteractionsEnabled"
                [canvasMode]="canvasMode"
                [executeNodeData]="getExecuteNodeData(node)"
                (deleteNode)="onDelete($event)"
                (moveNode)="onMove($event)"
                (nodeSelected)="onSelect($event)"
                (nodeDoubleClicked)="onDoubleClick($event)"
                (startConnection)="onStartConnection($event)"
                (nodePositionChanged)="onNodePositionChanged($event)"
              >
              </app-build-agent-node>
            </ng-template>
          </app-canvas-board>
        </div>
      </div>

      <!-- Playground Component - Shows when in execute mode -->
      <div class="playground-area" *ngIf="isExecuteMode && showChatInterface">
        <button
          class="exit-execute-btn"
          (click)="onExitExecuteMode()"
          title="Exit Execute Mode"
        ></button>

        <app-playground
          [messages]="chatMessages"
          [isLoading]="isProcessingChat"
          [agentType]="currentAgentType"
          [showChatInteractionToggles]="true"
          [showAiPrincipleToggle]="true"
          [showApprovalButton]="showApprovalButton"
          [showDropdown]="false"
          [showAgentNameInput]="true"
          [displayedAgentName]="agentName"
          [agentNamePlaceholder]="'Current Agent Name'"
          (promptChange)="onPromptChanged($event)"
          (messageSent)="handleChatMessage($event)"
          (conversationalToggle)="onPlaygroundConversationalToggle($event)"
          (templateToggle)="onPlaygroundTemplateToggle($event)"
          (filesSelected)="onFilesSelected($event)"
          (approvalRequested)="onApprovalRequested()"
        >
        </app-playground>
      </div>

      <!-- Preview Panel -->
      <div
        class="preview-panel"
        *ngIf="showPreview"
        [class.visible]="showPreview"
      >
        <div class="preview-header">
          <h3 class="preview-title">
            {{ previewData?.title || "Item Details" }}
          </h3>
          <button
            class="close-preview-btn"
            (click)="closePreview()"
            type="button"
          >
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>

        <div class="preview-content">
          <!-- Loading State -->
          <div *ngIf="isLoadingPreview" class="preview-loading">
            <div class="loading-spinner"></div>
            <p>Loading details...</p>
          </div>

          <!-- Error State -->
          <div *ngIf="previewData?.error" class="preview-error">
            <p>{{ previewData.error }}</p>
          </div>

          <!-- Model Preview -->
          <div
            *ngIf="previewData?.type === 'model' && previewData?.data"
            class="model-preview"
          >
            <div class="preview-field">
              <label>Model Name:</label>
              <span>{{ previewData.data.modelDeploymentName }}</span>
            </div>
            <div class="preview-field">
              <label>Description:</label>
              <span>{{
                previewData.data.modelDescription || "No description available"
              }}</span>
            </div>
            <div class="preview-field">
              <label>Model Type:</label>
              <span>{{ previewData.data.modelType }}</span>
            </div>
            <div class="preview-field">
              <label>AI Engine:</label>
              <span>{{ previewData.data.aiEngine }}</span>
            </div>
            <div class="preview-field">
              <label>Created:</label>
              <span>{{ previewData.data.date | date: "medium" }}</span>
            </div>
            <div class="preview-field">
              <label>Model ID:</label>
              <span>{{ previewData.data.id }}</span>
            </div>
          </div>

          <!-- Tool Preview -->
          <div
            *ngIf="previewData?.type === 'tool' && previewData?.data"
            class="tool-preview"
          >
            <div class="preview-field">
              <label>Tool Name:</label>
              <span>{{ previewData.data.name }}</span>
            </div>
            <div class="preview-field">
              <label>Description:</label>
              <span>{{ previewData.data.description }}</span>
            </div>
            <div class="preview-field" *ngIf="previewData.data.className">
              <label>Class Name:</label>
              <span>{{ previewData.data.className }}</span>
            </div>
            <div class="preview-field" *ngIf="previewData.data.createdBy">
              <label>Created by:</label>
              <span>{{ previewData.data.createdBy }}</span>
            </div>
            <div class="preview-field" *ngIf="previewData.data.createdOn">
              <label>Created on:</label>
              <span>{{ previewData.data.createdOn | date: "medium" }}</span>
            </div>
            <div class="preview-field">
              <label>Approved:</label>
              <span>{{ previewData.data.isApproved ? "Yes" : "No" }}</span>
            </div>
            <div class="preview-field" *ngIf="previewData.data.functionality">
              <label>Tool Definition:</label>
              <pre class="content-preview">{{
                previewData.data.functionality
              }}</pre>
            </div>
          </div>

          <!-- Prompt Preview -->
          <div
            *ngIf="previewData?.type === 'prompt' && previewData?.data"
            class="prompt-preview"
          >
            <div class="preview-field">
              <label>Name:</label>
              <span>{{ previewData.data.name }}</span>
            </div>
            <div class="preview-field">
              <label>Role:</label>
              <span>{{ previewData.data.role }}</span>
            </div>
            <div class="preview-field">
              <label>Goal:</label>
              <span>{{ previewData.data.goal }}</span>
            </div>
            <div class="preview-field">
              <label>Description:</label>
              <span>{{ previewData.data.description }}</span>
            </div>
            <div class="preview-field">
              <label>Backstory:</label>
              <span>{{ previewData.data.backstory }}</span>
            </div>
            <div class="preview-field">
              <label>Expected Output:</label>
              <span>{{ previewData.data.expectedOutput }}</span>
            </div>
            <div class="preview-field" *ngIf="previewData.data.categoryName">
              <label>Category:</label>
              <span>{{ previewData.data.categoryName }}</span>
            </div>
            <div class="preview-field" *ngIf="previewData.data.domainName">
              <label>Domain:</label>
              <span>{{ previewData.data.domainName }}</span>
            </div>
            <div class="preview-field" *ngIf="previewData.data.type">
              <label>Type:</label>
              <span>{{ previewData.data.type }}</span>
            </div>
            <div class="preview-field" *ngIf="previewData.data.updatedAt">
              <label>Updated:</label>
              <span>{{ previewData.data.updatedAt | date: "medium" }}</span>
            </div>
          </div>

          <!-- Knowledge Base Preview -->
          <div
            *ngIf="previewData?.type === 'knowledge' && previewData?.data"
            class="knowledge-preview"
          >
            <div class="preview-field">
              <label>Collection ID:</label>
              <span>{{ previewData.data.id }}</span>
            </div>
            <div class="preview-field">
              <label>Retriever Type:</label>
              <span>{{ previewData.data.retrieverType }}</span>
            </div>
            <div class="preview-field">
              <label>Total Files:</label>
              <span>{{ previewData.data.totalFiles }}</span>
            </div>
            <div
              class="preview-field"
              *ngIf="
                previewData.data.files && previewData.data.files.length > 0
              "
            >
              <label>Files:</label>
              <div class="files-list">
                <div
                  *ngFor="let file of previewData.data.files"
                  class="file-item"
                >
                  <strong>{{ file.fileName }}</strong>
                  <small
                    >{{ file.fileSizeBytes | number }} bytes -
                    {{ file.uploadDate | date: "short" }}</small
                  >
                </div>
              </div>
            </div>
          </div>

          <!-- Guardrail Preview -->
          <div
            *ngIf="previewData?.type === 'guardrail' && previewData?.data"
            class="guardrail-preview"
          >
            <div class="preview-field">
              <label>Label ID:</label>
              <span>{{ previewData.data.id }}</span>
            </div>
            <div class="preview-field">
              <label>Name:</label>
              <span>{{ previewData.data.name }}</span>
            </div>
            <div class="preview-field">
              <label>Description:</label>
              <span>{{ previewData.data.description }}</span>
            </div>
            <div class="preview-field" *ngIf="previewData.data.labelCode">
              <label>Label Code:</label>
              <span>{{ previewData.data.labelCode }}</span>
            </div>
            <div class="preview-field" *ngIf="previewData.data.categoryName">
              <label>Category:</label>
              <span>{{ previewData.data.categoryName }}</span>
            </div>
            <div class="preview-field" *ngIf="previewData.data.categoryId">
              <label>Category ID:</label>
              <span>{{ previewData.data.categoryId }}</span>
            </div>
            <!-- Show any additional fields that might be available -->
            <div
              class="preview-field"
              *ngFor="let field of getAdditionalFields(previewData.data)"
            >
              <label>{{ field.key }}:</label>
              <span>{{ field.value }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Success Popup -->
  <ava-popup
    [show]="showSuccessPopup"
    [title]="popupTitle"
    [message]="popupMessage"
    [showHeaderIcon]="true"
    headerIconName="check-circle"
    iconColor="#28a745"
    [showClose]="true"
    [showCancel]="false"
    [showConfirm]="true"
    [confirmButtonLabel]="'OK'"
    [confirmButtonVariant]="'primary'"
    [confirmButtonBackground]="'#28a745'"
    (confirm)="onSuccessConfirm()"
    (closed)="closeSuccessPopup()"
  >
  </ava-popup>

  <!-- Error Popup -->
  <ava-popup
    [show]="showErrorPopup"
    [title]="popupTitle"
    [message]="popupMessage"
    [showHeaderIcon]="true"
    headerIconName="alert-circle"
    iconColor="#dc3545"
    [showClose]="true"
    [showCancel]="false"
    [showConfirm]="true"
    [confirmButtonLabel]="'OK'"
    [confirmButtonVariant]="'primary'"
    [confirmButtonBackground]="'#dc3545'"
    (confirm)="closeErrorPopup()"
    (closed)="closeErrorPopup()"
  >
  </ava-popup>

  <!-- Warning Popup -->
  <ava-popup
    [show]="showWarningPopup"
    [title]="popupTitle"
    [message]="popupMessage"
    [showHeaderIcon]="true"
    headerIconName="alert-triangle"
    iconColor="#ffc107"
    [showClose]="true"
    [showCancel]="true"
    [showConfirm]="true"
    [confirmButtonLabel]="'Continue'"
    [cancelButtonLabel]="'Cancel'"
    [confirmButtonVariant]="'primary'"
    [confirmButtonBackground]="'#ffc107'"
    (confirm)="onWarningConfirm()"
    (cancel)="onWarningCancel()"
    (closed)="closeWarningPopup()"
  >
  </ava-popup>

  <!-- Prompt Configuration Popup -->
  <ava-popup
    [show]="showPromptTypePopup"
    title="Prompt Configuration"
    [message]="''"
    [showHeaderIcon]="false"
    [showClose]="true"
    [showCancel]="false"
    [showConfirm]="false"
    [popupWidth]="'90%'"
    (closed)="closePromptTypePopup()"
  >
    <div class="prompt-configuration-container">
      <!-- Single Column Layout - Figma Design -->
      <div class="main-column">
        <!-- Tab Container -->
        <div class="tab-container">
          <h3 class="tab-heading">Choose the type of prompt</h3>
          <div class="tabs-wrapper">
            <div class="pill-tabs-container">
              <ava-button
                [label]="'Freeform'"
                [variant]="selectedPromptType === 'freeform' ? 'primary' : 'secondary'"
                [size]="'medium'"
                (userClick)="onPromptTypeTabSelected('freeform')"
              ></ava-button>
              <ava-button
                [label]="'Template'"
                [variant]="selectedPromptType === 'template' ? 'primary' : 'secondary'"
                [size]="'medium'"
                (userClick)="onPromptTypeTabSelected('template')"
              ></ava-button>
            </div>
          </div>
        </div>

        <!-- Form Container -->
        <div class="form-container">
          <!-- Tab Content -->
          <div class="tab-content">
            <!-- Freeform Tab -->
            <div *ngIf="selectedPromptType === 'freeform'" class="freeform-content-wrapper">
              <div class="form-field">
                <label>Freeform Prompt *</label>
                <textarea
                  [(ngModel)]="promptFormData.promptTask"
                  rows="12"
                  placeholder="Please enter the prompt task"
                ></textarea>
              </div>

              <!-- Regenerate Button for Freeform -->
              <div class="regenerate-button-wrapper">
                <ava-button
                  [label]="'Regenerate'"
                  [variant]="'secondary'"
                  [size]="'small'"
                  (userClick)="regeneratePromptData()"
                ></ava-button>
              </div>
            </div>

            <!-- Template Tab -->
            <div *ngIf="selectedPromptType === 'template'" class="template-form">
              <!-- Role and Prompt Type Row -->
              <div class="fields-row">
                <div class="field-col">
                  <div class="role-dropdown-row">
                    <div class="form-field role-field">
                      <label>Role *</label>
                      <input type="text" [(ngModel)]="promptFormData.role" placeholder="Please enter role" />
                    </div>
                    <!-- <div class="form-field prompt-type-field">
                      <label>Prompt Type *</label>
                      <select [(ngModel)]="promptFormData.promptType">
                        <option value="">Select Prompt Type</option>
                        <option value="Zero Shot">Zero Shot</option>
                        <option value="Few Shot">Few Shot</option>
                        <option value="Chain of Thought">Chain of Thought</option>
                      </select>
                    </div> -->
                  </div>
                </div>
              </div>

              <!-- Goal and Description Row -->
              <div class="fields-row two-column">
                <div class="field-col">
                  <div class="form-field">
                    <label>Goal *</label>
                    <textarea [(ngModel)]="promptFormData.goal" rows="4" placeholder="Please enter goal"></textarea>
                  </div>
                </div>
                <div class="field-col">
                  <div class="form-field">
                    <label>Description *</label>
                    <textarea [(ngModel)]="promptFormData.description" rows="4" placeholder="Please enter description"></textarea>
                  </div>
                </div>
              </div>

              <!-- Backstory and Expected Output Row -->
              <div class="fields-row two-column">
                <div class="field-col">
                  <div class="form-field">
                    <label>Backstory *</label>
                    <textarea [(ngModel)]="promptFormData.backstory" rows="4" placeholder="Please enter backstory"></textarea>
                  </div>
                </div>
                <div class="field-col">
                  <div class="form-field">
                    <label>Expected Output *</label>
                    <textarea [(ngModel)]="promptFormData.expectedOutput" rows="4" placeholder="Please enter expected output"></textarea>
                  </div>
                </div>
              </div>

              <!-- Intermediate Steps (for Chain of Thought) -->
              <div class="fields-row" *ngIf="promptFormData.promptType === 'Chain of Thought'">
                <div class="field-col">
                  <div class="form-field">
                    <label>Intermediate Steps *</label>
                    <textarea [(ngModel)]="promptFormData.intermediateSteps" rows="4" placeholder="Please enter intermediate steps"></textarea>
                  </div>
                </div>
              </div>

              <!-- Regenerate Button for Freeform -->
              <div class="regenerate-button-wrapper">
                <ava-button
                  [label]="'Regenerate'"
                  [variant]="'secondary'"
                  [size]="'small'"
                  (userClick)="regeneratePromptData()"
                ></ava-button>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
          <ava-button
            [label]="'Cancel'"
            [variant]="'secondary'"
            [size]="'medium'"
            (userClick)="closePromptTypePopup()"
          ></ava-button>
          <ava-button
            [label]="'Proceed'"
            [variant]="'primary'"
            [size]="'medium'"
            (userClick)="onSavePrompt()"
          ></ava-button>
        </div>
      </div>
    </div>
  </ava-popup>

  <!-- Save Prompt Confirmation Popup -->
  <ava-popup
    [show]="showSavePromptPopup"
    title="Do you want to save this prompt?"
    [message]="''"
    [showHeaderIcon]="false"
    [showClose]="true"
    [showCancel]="true"
    [showConfirm]="true"
    [cancelButtonLabel]="'No'"
    [confirmButtonLabel]="'Yes'"
    [cancelButtonVariant]="'secondary'"
    [confirmButtonVariant]="'primary'"
    [popupWidth]="'400px'"
    (cancel)="onDontSavePrompt()"
    (confirm)="onSavePrompt()"
    (closed)="closeSavePromptPopup()"
  >
  </ava-popup>

  <!-- Approval Confirmation Popup removed - approval now handled through canvas button -->

  <!-- Save Confirmation Popup -->
  <ava-popup
    [show]="showSaveConfirmationPopup"
    title="Do you want to save this prompt?"
    [message]="''"
    [showHeaderIcon]="false"
    [showClose]="true"
    [showCancel]="true"
    [showConfirm]="true"
    [cancelButtonLabel]="'No'"
    [confirmButtonLabel]="'Yes'"
    [popupWidth]="'400px'"
    (cancel)="onDontSavePrompt()"
    (confirm)="onConfirmSavePrompt()"
    (closed)="closeSaveConfirmationPopup()"
  >
  </ava-popup>

  <!-- Final Save Popup -->
  <ava-popup
    [show]="showFinalSavePopup"
    [title]="''"
    [message]="''"
    [showHeaderIcon]="false"
    [showClose]="true"
    [showCancel]="false"
    [showConfirm]="false"
    [popupWidth]="'500px'"
    (closed)="closeFinalSavePopup()"
  >
    <div class="final-save-popup-content">
      <!-- Prompt Name Field -->
      <div class="form-field">
        <label class="field-label">
          Prompt Name <span class="required">*</span>
        </label>
        <input
          type="text"
          class="form-input"
          [(ngModel)]="finalSaveData.promptName"
          placeholder="New Tool Name"
        />
      </div>

      <!-- Prompt Description Field -->
      <div class="form-field">
        <label class="field-label">
          Prompt Description <span class="required">*</span>
        </label>
        <textarea
          class="form-textarea"
          [(ngModel)]="finalSaveData.promptDescription"
          placeholder="Enter text here"
          rows="6"
        ></textarea>
      </div>

      <!-- Action Buttons -->
      <div class="final-save-buttons">
        <ava-button
          [label]="'Close'"
          [variant]="'secondary'"
          [size]="'medium'"
          (userClick)="closeFinalSavePopup()">
        </ava-button>
        <ava-button
          [label]="'Save'"
          [variant]="'primary'"
          [size]="'medium'"
          (userClick)="onFinalSave()">
        </ava-button>
      </div>
    </div>
  </ava-popup>
</div>
