import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { KeyboardShortcutsService, KeyboardShortcut } from '../../services/keyboard-shortcuts.service';

/**
 * Component for displaying available keyboard shortcuts
 * Shows a modal or panel with all registered shortcuts organized by category
 */
@Component({
  selector: 'app-keyboard-shortcuts-help',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="shortcuts-help-overlay" [class.visible]="isVisible" (click)="onOverlayClick($event)">
      <div class="shortcuts-help-modal" (click)="$event.stopPropagation()">
        <!-- Header -->
        <div class="shortcuts-header">
          <h2 class="shortcuts-title">
            <i class="fas fa-keyboard"></i>
            Keyboard Shortcuts
          </h2>
          <button
            type="button"
            class="close-button"
            (click)="closeModal()"
            [attr.aria-label]="'Close shortcuts help'">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <!-- Content -->
        <div class="shortcuts-content">
          @if (shortcutCategories.length === 0) {
            <div class="no-shortcuts">
              <i class="fas fa-info-circle"></i>
              <p>No keyboard shortcuts are currently registered.</p>
            </div>
          } @else {
            @for (category of shortcutCategories; track category.name) {
              <div class="shortcut-category">
                <h3 class="category-title">{{ category.displayName }}</h3>
                <div class="shortcuts-list">
                  @for (shortcut of category.shortcuts; track shortcut.key) {
                    <div class="shortcut-item">
                      <div class="shortcut-keys">
                        <kbd class="shortcut-key">{{ formatShortcut(shortcut) }}</kbd>
                      </div>
                      <div class="shortcut-description">{{ shortcut.description }}</div>
                    </div>
                  }
                </div>
              </div>
            }
          }
        </div>

        <!-- Footer -->
        <div class="shortcuts-footer">
          <p class="shortcuts-note">
            <i class="fas fa-lightbulb"></i>
            Press <kbd>?</kbd> to toggle this help panel
          </p>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./keyboard-shortcuts-help.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class KeyboardShortcutsHelpComponent {
  private readonly keyboardShortcutsService = inject(KeyboardShortcutsService);
  
  @Input() isVisible: boolean = false;
  @Output() close = new EventEmitter<void>();
  
  /**
   * Get shortcuts organized by category
   */
  get shortcutCategories() {
    const shortcuts = this.keyboardShortcutsService.getShortcuts();
    const categories = new Map<string, KeyboardShortcut[]>();
    
    shortcuts.forEach(shortcut => {
      const category = shortcut.category || 'general';
      if (!categories.has(category)) {
        categories.set(category, []);
      }
      categories.get(category)!.push(shortcut);
    });
    
    return Array.from(categories.entries()).map(([name, shortcuts]) => ({
      name,
      displayName: this.getCategoryDisplayName(name),
      shortcuts: shortcuts.sort((a, b) => a.description.localeCompare(b.description))
    }));
  }
  
  /**
   * Format shortcut for display
   */
  formatShortcut(shortcut: KeyboardShortcut): string {
    return this.keyboardShortcutsService.formatShortcut(shortcut);
  }
  
  /**
   * Handle overlay click to close modal
   */
  onOverlayClick(event: Event): void {
    if (event.target === event.currentTarget) {
      this.closeModal();
    }
  }
  
  /**
   * Close the help modal
   */
  closeModal(): void {
    this.close.emit();
  }
  
  /**
   * Get display name for category
   */
  private getCategoryDisplayName(category: string): string {
    const displayNames: Record<string, string> = {
      general: 'General',
      projects: 'Projects',
      navigation: 'Navigation',
      editing: 'Editing'
    };
    
    return displayNames[category] || category.charAt(0).toUpperCase() + category.slice(1);
  }
}
