import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { IconPillComponent, IconOption } from './icon-pill.component';
import { IconsComponent } from '../icons/icons.component';

describe('IconPillComponent', () => {
  let component: IconPillComponent;
  let fixture: ComponentFixture<IconPillComponent>;

  const mockOptions: IconOption[] = [
    { name: 'Angular', icon: 'awe_modules', value: 'angular' },
    { name: 'React', icon: 'awe_react', value: 'react' },
    { name: 'Vue', icon: 'awe_toggled_button', value: 'vue' }
  ];

  const mockLocalSvgOption: IconOption = {
    name: 'Custom',
    icon: 'assets/custom.svg',
    value: 'custom',
    isLocalSvg: true
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [IconPillComponent, IconsComponent]
    }).compileComponents();

    fixture = TestBed.createComponent(IconPillComponent);
    component = fixture.componentInstance;
    component.options = mockOptions;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Initialization', () => {
    it('should set default selected option to first option if none provided', () => {
      expect(component.selectedOption).toEqual(mockOptions[0]);
    });

    it('should use provided selected option', () => {
      const customOption = mockOptions[1];
      component.selectedOption = customOption;
      fixture.detectChanges();
      expect(component.selectedOption).toEqual(customOption);
    });
  });

  describe('UI States', () => {
    it('should show only icon by default', () => {
      const iconWrapper = fixture.debugElement.query(By.css('.icon-wrapper'));
      const text = fixture.debugElement.query(By.css('.text'));
      
      expect(iconWrapper).toBeTruthy();
      expect(text).toBeFalsy();
    });

    it('should show text and arrow when hovered', () => {
      const container = fixture.debugElement.query(By.css('.icon-pill-container'));
      container.triggerEventHandler('mouseenter', {});
      fixture.detectChanges();

      const text = fixture.debugElement.query(By.css('.text'));
      const arrow = fixture.debugElement.query(By.css('.arrow'));
      
      expect(text).toBeTruthy();
      expect(text.nativeElement.textContent.trim()).toBe(mockOptions[0].name);
      expect(arrow).toBeTruthy();
    });

    it('should add expanded class when hovered', () => {
      const container = fixture.debugElement.query(By.css('.icon-pill-container'));
      container.triggerEventHandler('mouseenter', {});
      fixture.detectChanges();

      const button = fixture.debugElement.query(By.css('.icon-pill'));
      expect(button.nativeElement.classList.contains('expanded')).toBeTrue();
    });
  });

  describe('Dropdown Behavior', () => {
    it('should toggle dropdown on button click', () => {
      const button = fixture.debugElement.query(By.css('.icon-pill'));
      button.triggerEventHandler('click', { stopPropagation: () => {} });
      fixture.detectChanges();

      const dropdown = fixture.debugElement.query(By.css('.dropdown'));
      expect(dropdown.classes['show']).toBeTrue();
    });

    it('should show all options in dropdown', () => {
      const button = fixture.debugElement.query(By.css('.icon-pill'));
      button.triggerEventHandler('click', { stopPropagation: () => {} });
      fixture.detectChanges();

      const dropdownItems = fixture.debugElement.queryAll(By.css('.dropdown-item'));
      expect(dropdownItems.length).toBe(mockOptions.length);
    });
  });

  describe('Option Selection', () => {
    it('should emit selected option on click', () => {
      spyOn(component.selectionChange, 'emit');
      
      const button = fixture.debugElement.query(By.css('.icon-pill'));
      button.triggerEventHandler('click', { stopPropagation: () => {} });
      fixture.detectChanges();

      const dropdownItems = fixture.debugElement.queryAll(By.css('.dropdown-item'));
      dropdownItems[1].triggerEventHandler('click', { stopPropagation: () => {} });
      
      expect(component.selectionChange.emit).toHaveBeenCalledWith(mockOptions[1]);
    });
  });

  describe('Keyboard Navigation', () => {
    it('should select option on Enter key', () => {
      spyOn(component.selectionChange, 'emit');
      
      const button = fixture.debugElement.query(By.css('.icon-pill'));
      button.triggerEventHandler('click', { stopPropagation: () => {} });
      fixture.detectChanges();

      const dropdownItems = fixture.debugElement.queryAll(By.css('.dropdown-item'));
      dropdownItems[1].triggerEventHandler('keydown.enter', { stopPropagation: () => {} });
      
      expect(component.selectionChange.emit).toHaveBeenCalledWith(mockOptions[1]);
    });

    it('should select option on Space key', () => {
      spyOn(component.selectionChange, 'emit');
      
      const button = fixture.debugElement.query(By.css('.icon-pill'));
      button.triggerEventHandler('click', { stopPropagation: () => {} });
      fixture.detectChanges();

      const dropdownItems = fixture.debugElement.queryAll(By.css('.dropdown-item'));
      dropdownItems[1].triggerEventHandler('keydown.space', { stopPropagation: () => {} });
      
      expect(component.selectionChange.emit).toHaveBeenCalledWith(mockOptions[1]);
    });
  });

  describe('Accessibility', () => {
    it('should have correct ARIA attributes', () => {
      const button = fixture.debugElement.query(By.css('.icon-pill'));
      expect(button.attributes['aria-haspopup']).toBe('true');
      expect(button.attributes['aria-expanded']).toBe('false');
      expect(button.attributes['aria-label']).toBe('Select: Angular');
    });

    it('should update aria-expanded when dropdown opens', () => {
      const button = fixture.debugElement.query(By.css('.icon-pill'));
      button.triggerEventHandler('click', { stopPropagation: () => {} });
      fixture.detectChanges();

      expect(button.attributes['aria-expanded']).toBe('true');
    });

    it('should have role="menu" on dropdown', () => {
      const dropdown = fixture.debugElement.query(By.css('.dropdown'));
      expect(dropdown.attributes['role']).toBe('menu');
    });

    it('should have role="menuitem" on dropdown items', () => {
      const button = fixture.debugElement.query(By.css('.icon-pill'));
      button.triggerEventHandler('click', { stopPropagation: () => {} });
      fixture.detectChanges();

      const dropdownItems = fixture.debugElement.queryAll(By.css('.dropdown-item'));
      dropdownItems.forEach(item => {
        expect(item.attributes['role']).toBe('menuitem');
      });
    });
  });

  describe('Local SVG Support', () => {

    it('should render awe-icons when isLocalSvg is false', () => {
      const aweIcon = fixture.debugElement.query(By.css('awe-icons'));
      expect(aweIcon).toBeTruthy();
      expect(aweIcon.attributes['ng-reflect-icon-name']).toBe('awe_modules');
    });
  });
});