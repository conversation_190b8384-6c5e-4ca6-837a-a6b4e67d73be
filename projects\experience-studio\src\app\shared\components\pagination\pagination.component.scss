@use '../../../../../../public/assets/styles/mixins' as mixins;

// Pagination Component Styles
.pagination {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
  padding: 2rem 0;

  // CSS Custom Properties
  --pagination-btn-bg: var(--card-bg);
  --pagination-btn-border: var(--border-light);
  --pagination-btn-hover: var(--nav-item-hover);
  --pagination-btn-active: var(--primary-purple);
  --pagination-btn-disabled: var(--text-secondary);

  .pagination-info {
    .pagination-text {
      font-size: 0.875rem;
      color: var(--text-secondary);
      text-align: center;
    }
  }

  .pagination-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;

    .pagination-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 2.5rem;
      height: 2.5rem;
      padding: 0 0.75rem;
      background: var(--pagination-btn-bg);
      border: 1px solid var(--pagination-btn-border);
      border-radius: 0.375rem;
      color: var(--text-primary);
      font-size: 0.875rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      text-decoration: none;

      &:hover:not(:disabled) {
        background: var(--pagination-btn-hover);
        border-color: var(--primary-purple);
        color: var(--text-primary);
      }

      &:focus {
        outline: 2px solid var(--primary-purple);
        outline-offset: 1px;
      }

      &:disabled {
        background: var(--pagination-btn-bg);
        border-color: var(--pagination-btn-border);
        color: var(--pagination-btn-disabled);
        cursor: not-allowed;
        opacity: 0.5;
      }

      &.active {
        background: var(--pagination-btn-active);
        border-color: var(--pagination-btn-active);
        color: white;

        &:hover {
          background: #7c3aed;
          border-color: #7c3aed;
        }
      }

      &.prev-btn,
      &.next-btn {
        gap: 0.5rem;
        padding: 0 1rem;

        .btn-text {
          font-weight: 500;
        }
      }

      &.page-btn {
        min-width: 2.5rem;
        padding: 0;
        font-weight: 600;
      }
    }

    .page-numbers {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      margin: 0 0.5rem;

      .pagination-ellipsis {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 2.5rem;
        height: 2.5rem;
        color: var(--text-secondary);
        font-size: 0.875rem;
        font-weight: 600;
      }
    }
  }

  .page-size-selector {
    display: flex;
    align-items: center;
    gap: 0.75rem;

    .page-size-label {
      font-size: 0.875rem;
      color: var(--text-secondary);
      font-weight: 500;
    }

    .page-size-select {
      padding: 0.5rem 0.75rem;
      background: var(--pagination-btn-bg);
      border: 1px solid var(--pagination-btn-border);
      border-radius: 0.375rem;
      color: var(--text-primary);
      font-size: 0.875rem;
      cursor: pointer;
      transition: all 0.2s ease;

      &:focus {
        outline: 2px solid var(--primary-purple);
        outline-offset: 1px;
        border-color: var(--primary-purple);
      }

      &:hover {
        border-color: var(--primary-purple);
      }
    }
  }
}

// Compact pagination for mobile
@media (max-width: 768px) {
  .pagination {
    gap: 0.75rem;
    padding: 1.5rem 0;

    .pagination-controls {
      gap: 0.25rem;

      .pagination-btn {
        min-width: 2.25rem;
        height: 2.25rem;
        font-size: 0.8125rem;

        &.prev-btn,
        &.next-btn {
          padding: 0 0.75rem;

          .btn-text {
            display: none;
          }
        }
      }

      .page-numbers {
        margin: 0 0.25rem;

        .pagination-ellipsis {
          width: 2.25rem;
          height: 2.25rem;
        }
      }
    }

    .page-size-selector {
      flex-direction: column;
      gap: 0.5rem;
      text-align: center;

      .page-size-label {
        font-size: 0.8125rem;
      }

      .page-size-select {
        font-size: 0.8125rem;
      }
    }
  }
}

// Extra small screens
@media (max-width: 480px) {
  .pagination {
    .pagination-controls {
      flex-wrap: wrap;
      justify-content: center;

      .page-numbers {
        order: 2;
        width: 100%;
        justify-content: center;
        margin: 0.5rem 0 0;
      }

      .prev-btn {
        order: 1;
      }

      .next-btn {
        order: 3;
      }
    }
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .pagination {
    .pagination-controls .pagination-btn {
      border-width: 2px;

      &.active {
        border-width: 3px;
        font-weight: 700;
      }

      &:focus {
        outline-width: 3px;
      }
    }

    .page-size-selector .page-size-select {
      border-width: 2px;

      &:focus {
        outline-width: 3px;
      }
    }
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .pagination {
    * {
      transition: none !important;
    }
  }
}

// Print styles
@media print {
  .pagination {
    display: none;
  }
}

// Focus management for keyboard navigation
.pagination {
  .pagination-controls {
    .pagination-btn {
      &:focus-visible {
        outline: 2px solid var(--primary-purple);
        outline-offset: 2px;
        z-index: 1;
      }
    }
  }
}

// Loading state
.pagination {
  &[data-loading="true"] {
    opacity: 0.6;
    pointer-events: none;

    .pagination-controls .pagination-btn {
      cursor: not-allowed;
    }
  }
}

// Empty state
.pagination {
  &[data-empty="true"] {
    display: none;
  }
}

// Animation for page transitions
.pagination {
  .pagination-controls .pagination-btn {
    &.page-btn {
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
      }

      &:hover::before {
        left: 100%;
      }

      &.active::before {
        display: none;
      }
    }
  }
}

// Accessibility improvements
.pagination {
  [aria-current="page"] {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: -0.25rem;
      left: 50%;
      transform: translateX(-50%);
      width: 0.5rem;
      height: 0.125rem;
      background: var(--primary-purple);
      border-radius: 0.0625rem;
    }
  }
}
