@use '../../../../../../public/assets/styles/mixins' as mixins;

// Showcase Filters Component Styles
.showcase-filters {
  // CSS Custom Properties
  --filter-group-bg: transparent;
  --filter-group-border: var(--border-light);
  --filter-option-hover: var(--nav-item-hover);
  --checkbox-bg: var(--card-bg);
  --checkbox-border: var(--border-light);
  --checkbox-checked: var(--primary-purple);
  --radio-bg: var(--card-bg);
  --radio-border: var(--border-light);
  --radio-checked: var(--primary-purple);

  .filters-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;

    .filters-title {
      font-size: 1.125rem;
      font-weight: 600;
      color: var(--text-primary);
      margin: 0;
    }

    .clear-all-btn {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 0.75rem;
      background: rgba(239, 68, 68, 0.1);
      border: 1px solid #ef4444;
      border-radius: 0.375rem;
      color: #ef4444;
      font-size: 0.75rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: #ef4444;
        color: white;
      }
    }
  }

  .filter-groups {
    display: flex;
    flex-direction: column;
    gap: 1rem;

    .filter-group {
      border: 1px solid var(--filter-group-border);
      border-radius: 0.5rem;
      overflow: hidden;
      transition: all 0.2s ease;

      &.expanded {
        .filter-group-header i {
          transform: rotate(180deg);
        }
      }

      .filter-group-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        padding: 0.875rem 1rem;
        background: var(--filter-group-bg);
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: var(--filter-option-hover);
        }

        .group-title {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          font-size: 0.875rem;
          font-weight: 600;
          color: var(--text-primary);

          i {
            color: var(--primary-purple);
            width: 1rem;
            text-align: center;
          }

          .active-count {
            background: var(--primary-purple);
            color: white;
            font-size: 0.75rem;
            font-weight: 500;
            padding: 0.125rem 0.375rem;
            border-radius: 0.75rem;
            min-width: 1.25rem;
            height: 1.25rem;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }

        i {
          color: var(--text-secondary);
          transition: transform 0.2s ease;
        }
      }

      .filter-group-content {
        padding: 0 1rem 1rem;
        border-top: 1px solid var(--filter-group-border);

        .filter-search {
          position: relative;
          margin-bottom: 0.75rem;

          .search-input {
            width: 100%;
            padding: 0.5rem 0.75rem 0.5rem 2rem;
            background: var(--checkbox-bg);
            border: 1px solid var(--checkbox-border);
            border-radius: 0.375rem;
            font-size: 0.875rem;
            color: var(--text-primary);
            outline: none;
            transition: all 0.2s ease;

            &:focus {
              border-color: var(--primary-purple);
              box-shadow: 0 0 0 2px rgba(140, 101, 247, 0.1);
            }

            &::placeholder {
              color: var(--text-secondary);
            }
          }

          .search-icon {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            font-size: 0.875rem;
          }
        }

        .filter-option {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          padding: 0.5rem 0;
          cursor: pointer;
          transition: all 0.2s ease;
          position: relative;

          &:hover {
            background: var(--filter-option-hover);
            margin: 0 -0.5rem;
            padding-left: 0.5rem;
            padding-right: 0.5rem;
            border-radius: 0.375rem;
          }

          input[type="checkbox"],
          input[type="radio"] {
            position: absolute;
            opacity: 0;
            cursor: pointer;
          }

          .checkbox-custom {
            width: 1rem;
            height: 1rem;
            background: var(--checkbox-bg);
            border: 2px solid var(--checkbox-border);
            border-radius: 0.25rem;
            position: relative;
            transition: all 0.2s ease;
            flex-shrink: 0;

            &::after {
              content: '';
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%) scale(0);
              width: 0.375rem;
              height: 0.625rem;
              border: 2px solid white;
              border-top: none;
              border-left: none;
              transform-origin: center;
              transition: transform 0.2s ease;
            }
          }

          .radio-custom {
            width: 1rem;
            height: 1rem;
            background: var(--radio-bg);
            border: 2px solid var(--radio-border);
            border-radius: 50%;
            position: relative;
            transition: all 0.2s ease;
            flex-shrink: 0;

            &::after {
              content: '';
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%) scale(0);
              width: 0.375rem;
              height: 0.375rem;
              background: white;
              border-radius: 50%;
              transition: transform 0.2s ease;
            }
          }

          input[type="checkbox"]:checked + .checkbox-custom {
            background: var(--checkbox-checked);
            border-color: var(--checkbox-checked);

            &::after {
              transform: translate(-50%, -50%) scale(1) rotate(45deg);
            }
          }

          input[type="radio"]:checked + .radio-custom {
            background: var(--radio-checked);
            border-color: var(--radio-checked);

            &::after {
              transform: translate(-50%, -50%) scale(1);
            }
          }

          .option-label {
            flex: 1;
            font-size: 0.875rem;
            color: var(--text-primary);
            font-weight: 500;
          }

          .option-count {
            font-size: 0.75rem;
            color: var(--text-secondary);
            font-weight: 400;
          }

          &.radio-option {
            .option-label {
              font-weight: 400;
            }
          }

          &.creator-option {
            .option-label {
              font-weight: 500;
            }
          }
        }

        .quick-ranges {
          display: flex;
          flex-wrap: wrap;
          gap: 0.5rem;
          margin-bottom: 1rem;

          .quick-range-btn {
            padding: 0.375rem 0.75rem;
            background: transparent;
            border: 1px solid var(--checkbox-border);
            border-radius: 0.375rem;
            color: var(--text-secondary);
            font-size: 0.75rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
              background: var(--filter-option-hover);
              color: var(--text-primary);
            }

            &.active {
              background: var(--primary-purple);
              border-color: var(--primary-purple);
              color: white;
            }
          }
        }

        .custom-date-range {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 0.75rem;
          margin-bottom: 1rem;

          .date-input-group {
            display: flex;
            flex-direction: column;
            gap: 0.375rem;

            .date-label {
              font-size: 0.75rem;
              font-weight: 500;
              color: var(--text-secondary);
            }

            .date-input {
              padding: 0.5rem 0.75rem;
              background: var(--checkbox-bg);
              border: 1px solid var(--checkbox-border);
              border-radius: 0.375rem;
              font-size: 0.875rem;
              color: var(--text-primary);
              outline: none;
              transition: all 0.2s ease;

              &:focus {
                border-color: var(--primary-purple);
                box-shadow: 0 0 0 2px rgba(140, 101, 247, 0.1);
              }
            }
          }
        }

        .clear-date-range,
        .clear-creator {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.375rem 0.75rem;
          background: rgba(239, 68, 68, 0.1);
          border: 1px solid #ef4444;
          border-radius: 0.375rem;
          color: #ef4444;
          font-size: 0.75rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background: #ef4444;
            color: white;
          }
        }

        .no-results {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.5rem;
          padding: 1rem;
          color: var(--text-secondary);
          font-size: 0.875rem;
          text-align: center;

          i {
            opacity: 0.5;
          }
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .showcase-filters {
    .filter-groups .filter-group .filter-group-content {
      .custom-date-range {
        grid-template-columns: 1fr;
      }

      .quick-ranges {
        .quick-range-btn {
          flex: 1;
          text-align: center;
        }
      }
    }
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .showcase-filters {
    .filter-groups .filter-group {
      border-width: 2px;

      .filter-group-content .filter-option {
        .checkbox-custom,
        .radio-custom {
          border-width: 3px;
        }
      }
    }
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .showcase-filters {
    * {
      transition: none !important;
    }

    .filter-groups .filter-group.expanded .filter-group-header i {
      transform: none !important;
    }
  }
}
